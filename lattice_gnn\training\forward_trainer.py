"""
Training module for Forward Prediction Model.

This module implements the training loop, validation, and evaluation
for the forward property prediction task.

Features:
- Training loop with early stopping
- Learning rate scheduling
- Model checkpointing
- Comprehensive evaluation metrics
- Visualization of training progress
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch_geometric.loader import DataLoader as GeometricDataLoader
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import os
from pathlib import Path
import json
import time
from tqdm import tqdm
import matplotlib.pyplot as plt

# Import model and utilities
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from models.forward_model import ForwardModel, PropertyLoss, create_forward_model
    from utils.metrics import RegressionMetrics, PropertySpecificMetrics, print_metrics_summary
except ImportError:
    # Handle import errors gracefully for testing
    pass


class ForwardTrainer:
    """
    Trainer class for forward property prediction model.
    """
    
    def __init__(self,
                 model: ForwardModel,
                 train_loader: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oa<PERSON>,
                 val_loader: GeometricDataLoader,
                 test_loader: Optional[GeometricDataLoader] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        Initialize the trainer.
        
        Args:
            model: Forward prediction model
            train_loader: Training data loader
            val_loader: Validation data loader
            test_loader: Test data loader (optional)
            config: Training configuration
        """
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        
        # Default configuration
        default_config = {
            'learning_rate': 1e-3,
            'weight_decay': 1e-4,
            'num_epochs': 100,
            'patience': 10,
            'min_delta': 1e-4,
            'scheduler_type': 'cosine',
            'scheduler_params': {'T_max': 100},
            'loss_type': 'mse',
            'property_weights': None,
            'gradient_clip': 1.0,
            'save_dir': 'experiments/results',
            'save_best': True,
            'log_interval': 10
        }
        
        self.config = {**default_config, **(config or {})}
        
        # Setup device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # Setup optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # Setup loss function
        self.criterion = PropertyLoss(
            property_weights=self.config['property_weights'],
            loss_type=self.config['loss_type']
        )
        
        # Setup learning rate scheduler
        self._setup_scheduler()
        
        # Training state
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
        
        # Setup save directory
        self.save_dir = Path(self.config['save_dir'])
        self.save_dir.mkdir(parents=True, exist_ok=True)
    
    def _setup_scheduler(self):
        """Setup learning rate scheduler."""
        scheduler_type = self.config['scheduler_type']
        scheduler_params = self.config['scheduler_params']
        
        if scheduler_type == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, **scheduler_params
            )
        elif scheduler_type == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, **scheduler_params
            )
        elif scheduler_type == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', **scheduler_params
            )
        else:
            self.scheduler = None
    
    def train_epoch(self) -> float:
        """
        Train for one epoch.
        
        Returns:
            Average training loss
        """
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch}')
        
        for batch in progress_bar:
            batch = batch.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            predictions = self.model(batch)
            
            # Compute loss
            loss = self.criterion(predictions, batch.y)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.config['gradient_clip'] > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config['gradient_clip']
                )
            
            self.optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            progress_bar.set_postfix({'loss': loss.item()})
        
        return total_loss / num_batches
    
    def validate(self) -> Tuple[float, Dict]:
        """
        Validate the model.
        
        Returns:
            Tuple of (validation_loss, metrics_dict)
        """
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in self.val_loader:
                batch = batch.to(self.device)
                
                predictions = self.model(batch)
                loss = self.criterion(predictions, batch.y)
                
                total_loss += loss.item()
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(batch.y.cpu().numpy())
        
        # Compute metrics
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        metrics = RegressionMetrics.compute_all_metrics(targets, predictions)
        
        return total_loss / len(self.val_loader), metrics
    
    def train(self) -> Dict:
        """
        Complete training loop.
        
        Returns:
            Training history dictionary
        """
        print(f"Starting training on {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(self.config['num_epochs']):
            self.current_epoch = epoch
            
            # Training
            train_loss = self.train_epoch()
            
            # Validation
            val_loss, val_metrics = self.validate()
            
            # Update learning rate
            if self.scheduler is not None:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_loss)
                else:
                    self.scheduler.step()
            
            # Record history
            current_lr = self.optimizer.param_groups[0]['lr']
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['learning_rate'].append(current_lr)
            
            # Logging
            if epoch % self.config['log_interval'] == 0:
                print(f"\nEpoch {epoch}:")
                print(f"  Train Loss: {train_loss:.6f}")
                print(f"  Val Loss: {val_loss:.6f}")
                print(f"  Val R²: {val_metrics['overall']['r2']:.4f}")
                print(f"  Learning Rate: {current_lr:.2e}")
            
            # Early stopping check
            if val_loss < self.best_val_loss - self.config['min_delta']:
                self.best_val_loss = val_loss
                self.patience_counter = 0
                
                # Save best model
                if self.config['save_best']:
                    self.save_checkpoint('best_model.pt', val_metrics)
            else:
                self.patience_counter += 1
            
            # Early stopping
            if self.patience_counter >= self.config['patience']:
                print(f"\nEarly stopping at epoch {epoch}")
                break
        
        # Save final model and history
        self.save_checkpoint('final_model.pt')
        self.save_training_history()
        
        return self.training_history
    
    def evaluate(self, data_loader: Optional[GeometricDataLoader] = None) -> Dict:
        """
        Evaluate the model on test data.
        
        Args:
            data_loader: Data loader to evaluate on (defaults to test_loader)
            
        Returns:
            Evaluation metrics
        """
        if data_loader is None:
            data_loader = self.test_loader
        
        if data_loader is None:
            raise ValueError("No test data loader provided")
        
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in tqdm(data_loader, desc='Evaluating'):
                batch = batch.to(self.device)
                predictions = self.model(batch)
                
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(batch.y.cpu().numpy())
        
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        # Compute comprehensive metrics
        metrics = RegressionMetrics.compute_all_metrics(targets, predictions)
        
        # Add property-specific metrics
        mech_metrics = PropertySpecificMetrics.mechanical_property_metrics(
            targets, predictions
        )
        metrics['mechanical_specific'] = mech_metrics
        
        return metrics
    
    def save_checkpoint(self, filename: str, metrics: Optional[Dict] = None):
        """Save model checkpoint."""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_loss': self.best_val_loss,
            'config': self.config,
            'metrics': metrics
        }
        
        torch.save(checkpoint, self.save_dir / filename)
    
    def load_checkpoint(self, filename: str):
        """Load model checkpoint."""
        checkpoint = torch.load(self.save_dir / filename, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_val_loss = checkpoint['best_val_loss']
        
        return checkpoint.get('metrics', {})
    
    def save_training_history(self):
        """Save training history to file."""
        history_file = self.save_dir / 'training_history.json'
        with open(history_file, 'w') as f:
            json.dump(self.training_history, f, indent=2)
    
    def plot_training_curves(self, save_plot: bool = True):
        """Plot training curves."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Loss curves
        epochs = range(len(self.training_history['train_loss']))
        ax1.plot(epochs, self.training_history['train_loss'], label='Train')
        ax1.plot(epochs, self.training_history['val_loss'], label='Validation')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True)
        
        # Learning rate
        ax2.plot(epochs, self.training_history['learning_rate'])
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Learning Rate')
        ax2.set_title('Learning Rate Schedule')
        ax2.set_yscale('log')
        ax2.grid(True)
        
        plt.tight_layout()
        
        if save_plot:
            plt.savefig(self.save_dir / 'training_curves.png', dpi=300, bbox_inches='tight')
        
        plt.show()


def create_trainer(model: ForwardModel,
                  train_loader: GeometricDataLoader,
                  val_loader: GeometricDataLoader,
                  test_loader: Optional[GeometricDataLoader] = None,
                  config: Optional[Dict[str, Any]] = None) -> ForwardTrainer:
    """
    Factory function to create trainer.
    
    Args:
        model: Forward prediction model
        train_loader: Training data loader
        val_loader: Validation data loader
        test_loader: Test data loader
        config: Training configuration
        
    Returns:
        Initialized trainer
    """
    return ForwardTrainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        test_loader=test_loader,
        config=config
    )
