"""
Forward Prediction Model for Lattice Property Prediction.

This module implements the forward prediction branch that takes lattice
structures as input and predicts their mechanical and geometric properties.

The model consists of:
- Shared GNN backbone for structure encoding
- Multi-task prediction heads for different property types
- Property-specific loss functions and regularization
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import numpy as np

from .gnn_backbone import GNNBackbone, create_gnn_backbone


class PropertyPredictionHead(nn.Module):
    """
    Multi-layer perceptron for property prediction with residual connections.
    """
    
    def __init__(self,
                 input_dim: int,
                 output_dim: int,
                 hidden_dims: List[int] = [128, 64],
                 dropout: float = 0.1,
                 activation: str = 'relu',
                 use_batch_norm: bool = True):
        """
        Initialize property prediction head.
        
        Args:
            input_dim: Input feature dimension
            output_dim: Number of properties to predict
            hidden_dims: List of hidden layer dimensions
            dropout: Dropout probability
            activation: Activation function ('relu', 'gelu', 'swish')
            use_batch_norm: Whether to use batch normalization
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # Build layers
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            if activation == 'relu':
                layers.append(nn.ReLU())
            elif activation == 'gelu':
                layers.append(nn.GELU())
            elif activation == 'swish':
                layers.append(nn.SiLU())
            
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.layers = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize layer weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            x: Input features (batch_size, input_dim)
            
        Returns:
            Property predictions (batch_size, output_dim)
        """
        return self.layers(x)


class MultiTaskPredictionHead(nn.Module):
    """
    Multi-task prediction head with separate heads for different property groups.
    """
    
    def __init__(self,
                 input_dim: int,
                 property_groups: Dict[str, int],
                 shared_hidden_dims: List[int] = [256, 128],
                 task_hidden_dims: List[int] = [64],
                 dropout: float = 0.1):
        """
        Initialize multi-task prediction head.
        
        Args:
            input_dim: Input feature dimension
            property_groups: Dictionary mapping group names to output dimensions
            shared_hidden_dims: Hidden dimensions for shared layers
            task_hidden_dims: Hidden dimensions for task-specific layers
            dropout: Dropout probability
        """
        super().__init__()
        
        self.property_groups = property_groups
        
        # Shared layers
        shared_layers = []
        prev_dim = input_dim
        
        for hidden_dim in shared_hidden_dims:
            shared_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*shared_layers)
        
        # Task-specific heads
        self.task_heads = nn.ModuleDict()
        
        for group_name, output_dim in property_groups.items():
            self.task_heads[group_name] = PropertyPredictionHead(
                input_dim=prev_dim,
                output_dim=output_dim,
                hidden_dims=task_hidden_dims,
                dropout=dropout
            )
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass through multi-task head.
        
        Args:
            x: Input features (batch_size, input_dim)
            
        Returns:
            Dictionary mapping group names to predictions
        """
        # Shared processing
        shared_features = self.shared_layers(x)
        
        # Task-specific predictions
        predictions = {}
        for group_name, head in self.task_heads.items():
            predictions[group_name] = head(shared_features)
        
        return predictions


class ForwardModel(nn.Module):
    """
    Complete forward prediction model for lattice property prediction.
    """
    
    def __init__(self,
                 backbone_config: Dict[str, Any],
                 num_properties: int = 18,
                 use_multi_task: bool = True,
                 property_groups: Optional[Dict[str, int]] = None,
                 prediction_head_config: Optional[Dict[str, Any]] = None):
        """
        Initialize forward prediction model.
        
        Args:
            backbone_config: Configuration for GNN backbone
            num_properties: Total number of properties to predict
            use_multi_task: Whether to use multi-task learning
            property_groups: Property groups for multi-task learning
            prediction_head_config: Configuration for prediction head
        """
        super().__init__()
        
        self.num_properties = num_properties
        self.use_multi_task = use_multi_task
        
        # Create GNN backbone
        self.backbone = create_gnn_backbone(backbone_config)
        
        # Default property groups
        if property_groups is None:
            property_groups = {
                'mechanical_youngs': 3,      # Ex, Ey, Ez
                'mechanical_shear': 3,       # Gyz, Gxz, Gxy
                'mechanical_poisson': 6,     # Poisson ratios
                'scaling_constants': 3,      # Cx, Cy, Cz
                'scaling_exponents': 3       # nx, ny, nz
            }
        
        self.property_groups = property_groups
        
        # Default prediction head config
        if prediction_head_config is None:
            prediction_head_config = {
                'hidden_dims': [128, 64],
                'dropout': 0.1,
                'activation': 'relu'
            }
        
        # Create prediction head
        hidden_dim = backbone_config.get('hidden_dim', 256)
        
        if use_multi_task:
            self.prediction_head = MultiTaskPredictionHead(
                input_dim=hidden_dim,
                property_groups=property_groups,
                **prediction_head_config
            )
        else:
            self.prediction_head = PropertyPredictionHead(
                input_dim=hidden_dim,
                output_dim=num_properties,
                **prediction_head_config
            )
    
    def forward(self, data) -> torch.Tensor:
        """
        Forward pass through the complete model.
        
        Args:
            data: PyTorch Geometric Data object
            
        Returns:
            Property predictions (batch_size, num_properties) or
            Dictionary of predictions if using multi-task learning
        """
        # Get graph representation from backbone
        graph_repr, _ = self.backbone(data)
        
        # Predict properties
        predictions = self.prediction_head(graph_repr)
        
        if self.use_multi_task:
            # Concatenate multi-task predictions
            pred_list = []
            for group_name in ['mechanical_youngs', 'mechanical_shear', 
                             'mechanical_poisson', 'scaling_constants', 
                             'scaling_exponents']:
                if group_name in predictions:
                    pred_list.append(predictions[group_name])
            
            if pred_list:
                predictions = torch.cat(pred_list, dim=1)
        
        return predictions
    
    def predict_properties(self, data) -> Dict[str, torch.Tensor]:
        """
        Predict properties and return detailed breakdown.
        
        Args:
            data: PyTorch Geometric Data object
            
        Returns:
            Dictionary containing detailed predictions
        """
        self.eval()
        with torch.no_grad():
            graph_repr, node_repr = self.backbone(data)
            
            if self.use_multi_task:
                predictions = self.prediction_head(graph_repr)
                
                # Add combined prediction
                pred_list = []
                for group_name in ['mechanical_youngs', 'mechanical_shear', 
                                 'mechanical_poisson', 'scaling_constants', 
                                 'scaling_exponents']:
                    if group_name in predictions:
                        pred_list.append(predictions[group_name])
                
                if pred_list:
                    predictions['combined'] = torch.cat(pred_list, dim=1)
            else:
                combined_pred = self.prediction_head(graph_repr)
                predictions = {'combined': combined_pred}
            
            return predictions


class PropertyLoss(nn.Module):
    """
    Custom loss function for property prediction with property-specific weighting.
    """
    
    def __init__(self,
                 property_weights: Optional[torch.Tensor] = None,
                 loss_type: str = 'mse',
                 reduction: str = 'mean'):
        """
        Initialize property loss.
        
        Args:
            property_weights: Weights for different properties
            loss_type: Type of loss ('mse', 'mae', 'huber')
            reduction: Reduction method ('mean', 'sum', 'none')
        """
        super().__init__()
        
        self.property_weights = property_weights
        self.loss_type = loss_type
        self.reduction = reduction
        
        if loss_type == 'mse':
            self.base_loss = nn.MSELoss(reduction='none')
        elif loss_type == 'mae':
            self.base_loss = nn.L1Loss(reduction='none')
        elif loss_type == 'huber':
            self.base_loss = nn.HuberLoss(reduction='none')
        else:
            raise ValueError(f"Unknown loss type: {loss_type}")
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Compute weighted property loss.
        
        Args:
            predictions: Predicted properties (batch_size, num_properties)
            targets: Target properties (batch_size, num_properties)
            
        Returns:
            Computed loss
        """
        # Compute base loss
        loss = self.base_loss(predictions, targets)
        
        # Apply property weights
        if self.property_weights is not None:
            if self.property_weights.device != loss.device:
                self.property_weights = self.property_weights.to(loss.device)
            loss = loss * self.property_weights.unsqueeze(0)
        
        # Apply reduction
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss


def create_property_weights(property_ranges: Dict[str, float],
                          weight_strategy: str = 'inverse_range') -> torch.Tensor:
    """
    Create property weights based on value ranges.
    
    Args:
        property_ranges: Dictionary mapping property names to value ranges
        weight_strategy: Strategy for computing weights
        
    Returns:
        Property weights tensor
    """
    ranges = list(property_ranges.values())
    
    if weight_strategy == 'inverse_range':
        # Higher weight for properties with smaller ranges
        weights = [1.0 / (r + 1e-8) for r in ranges]
    elif weight_strategy == 'uniform':
        # Equal weights for all properties
        weights = [1.0] * len(ranges)
    elif weight_strategy == 'normalized_inverse':
        # Normalized inverse range weights
        inv_ranges = [1.0 / (r + 1e-8) for r in ranges]
        total = sum(inv_ranges)
        weights = [w / total * len(inv_ranges) for w in inv_ranges]
    else:
        raise ValueError(f"Unknown weight strategy: {weight_strategy}")
    
    return torch.tensor(weights, dtype=torch.float32)


def create_forward_model(config: Dict[str, Any]) -> ForwardModel:
    """
    Factory function to create forward model from configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Initialized forward model
    """
    backbone_config = config.get('backbone', {})
    
    return ForwardModel(
        backbone_config=backbone_config,
        num_properties=config.get('num_properties', 18),
        use_multi_task=config.get('use_multi_task', True),
        property_groups=config.get('property_groups', None),
        prediction_head_config=config.get('prediction_head', None)
    )
