import pandas as pd
import re
import numpy as np

def parse_lattice_data(file_path):
    """
    解析点阵结构数据文件并提取所有结构信息
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 分割每个结构的数据块
    structures = re.split(r'-{80,}', content)
    
    # 用于存储所有数据的列表
    node_data = []
    edge_data = []
    lattice_data = []
    
    for structure_block in structures:
        if not structure_block.strip() or 'Name:' not in structure_block:
            continue
            
        # 提取结构名称
        name_match = re.search(r'Name:\s+(\S+)', structure_block)
        if not name_match:
            continue
        lattice_name = name_match.group(1)
        
        # 提取其他名称
        other_names_match = re.search(r'Other name\(s\):\s+(.+)', structure_block)
        other_names = other_names_match.group(1) if other_names_match else ""
        
        # 提取单元胞参数
        unit_cell_match = re.search(r'Normalized unit cell parameters.*?:\s*([0-9.]+),\s*([0-9.]+),\s*([0-9.]+),\s*([0-9.]+),\s*([0-9.]+),\s*([0-9.]+)', structure_block)
        if not unit_cell_match:
            continue
            
        a, b, c, alpha, beta, gamma = map(float, unit_cell_match.groups())
        
        # 提取平均连接数
        avg_conn_match = re.search(r'Average connectivity:\s+Z_avg\s*=\s*([0-9.]+)', structure_block)
        avg_connectivity = float(avg_conn_match.group(1)) if avg_conn_match else 0.0
        
        # 检查是否有重叠或相交的杆件
        has_overlapping = 'overlapping or intersecting bars' in structure_block.lower()
        
        # 提取机械性能参数
        mech_match = re.search(r'Effective normalized mechanical properties:\s*Ex\s*=\s*([0-9.E-]+),\s*Ey\s*=\s*([0-9.E-]+),\s*Ez\s*=\s*([0-9.E-]+)\s*Gyz\s*=\s*([0-9.E-]+),\s*Gxz\s*=\s*([0-9.E-]+),\s*Gxy\s*=\s*([0-9.E-]+)\s*nuyz\s*=\s*([-0-9.]+),\s*nuxz\s*=\s*([-0-9.]+),\s*nuxy\s*=\s*([-0-9.]+),\s*nuzy\s*=\s*([-0-9.]+),\s*nuzx\s*=\s*([-0-9.]+),\s*nuyx\s*=\s*([-0-9.]+)', structure_block, re.DOTALL)
        
        if not mech_match:
            continue
            
        Ex, Ey, Ez, Gyz, Gxz, Gxy, nuyz, nuxz, nuxy, nuzy, nuzx, nuyx = map(float, mech_match.groups())
        
        # 提取缩放参数
        scaling_match = re.search(r'Scaling constants\s+Cx\s*=\s*([0-9.]+),\s*Cy\s*=\s*([0-9.]+),\s*Cz\s*=\s*([0-9.]+)\s*Scaling exponents\s+nx\s*=\s*([0-9.]+),\s*ny\s*=\s*([0-9.]+),\s*nz\s*=\s*([0-9.]+)', structure_block)
        
        if scaling_match:
            Cx, Cy, Cz, nx, ny, nz = map(float, scaling_match.groups())
        else:
            Cx = Cy = Cz = nx = ny = nz = 0.0
        
        # 提取节点位置
        nodes_section = re.search(r'Nodal positions:\s*(.*?)Bar connectivities:', structure_block, re.DOTALL)
        if not nodes_section:
            continue
            
        node_lines = [line.strip() for line in nodes_section.group(1).strip().split('\n') if line.strip()]
        nodes = []
        for i, line in enumerate(node_lines):
            coords = line.split()
            if len(coords) >= 3:
                x, y, z = map(float, coords[:3])
                nodes.append((i+1, x, y, z))  # 节点ID从1开始
                node_data.append({
                    'lattice_name': lattice_name,
                    'node_id': i+1,
                    'x': x,
                    'y': y,
                    'z': z
                })
        
        # 提取边连接信息
        edges_section = re.search(r'Bar connectivities:\s*(.*?)(?=\n\n|\Z)', structure_block, re.DOTALL)
        if not edges_section:
            continue
            
        edge_lines = [line.strip() for line in edges_section.group(1).strip().split('\n') if line.strip()]
        edges = []
        for line in edge_lines:
            edge_parts = line.split()
            if len(edge_parts) >= 2:
                source = int(edge_parts[0])
                target = int(edge_parts[1])
                edges.append((source, target))
                edge_data.append({
                    'lattice_name': lattice_name,
                    'source_node_original': source,
                    'target_node_original': target
                })
        
        # 添加到晶格数据
        lattice_data.append({
            'lattice_name': lattice_name,
            'num_nodes': len(nodes),
            'num_edges': len(edges),
            'avg_connectivity': avg_connectivity,
            'has_overlapping_bars': has_overlapping,
            'unit_cell_a': a,
            'unit_cell_b': b,
            'unit_cell_c': c,
            'unit_cell_alpha': alpha,
            'unit_cell_beta': beta,
            'unit_cell_gamma': gamma,
            'mech_Ex': Ex,
            'mech_Ey': Ey,
            'mech_Ez': Ez,
            'mech_Gyz': Gyz,
            'mech_Gxz': Gxz,
            'mech_Gxy': Gxy,
            'mech_nuyz': nuyz,
            'mech_nuxz': nuxz,
            'mech_nuxy': nuxy,
            'mech_nuzy': nuzy,
            'mech_nuzx': nuzx,
            'mech_nuyx': nuyx,
            'scaling_Cx': Cx,
            'scaling_Cy': Cy,
            'scaling_Cz': Cz,
            'scaling_nx': nx,
            'scaling_ny': ny,
            'scaling_nz': nz
        })
        
        print(f"已处理结构: {lattice_name}, 节点数: {len(nodes)}, 边数: {len(edges)}")
    
    return node_data, edge_data, lattice_data

def save_to_csv(node_data, edge_data, lattice_data):
    """
    将数据保存为CSV文件
    """
    # 保存节点数据
    node_df = pd.DataFrame(node_data)
    node_df.to_csv('node.csv', index=False)
    print(f"节点数据已保存到 node.csv，共 {len(node_df)} 条记录")
    
    # 保存边数据
    edge_df = pd.DataFrame(edge_data)
    edge_df.to_csv('edge.csv', index=False)
    print(f"边数据已保存到 edge.csv，共 {len(edge_df)} 条记录")
    
    # 保存晶格数据
    lattice_df = pd.DataFrame(lattice_data)
    lattice_df.to_csv('lattice.csv', index=False)
    print(f"晶格数据已保存到 lattice.csv，共 {len(lattice_df)} 条记录")
    
    return node_df, edge_df, lattice_df

def main():
    """
    主函数
    """
    # 解析数据文件
    file_path = 'Unit_Cell.txt'  # 请确保文件在当前目录下
    
    try:
        print("开始解析数据文件...")
        node_data, edge_data, lattice_data = parse_lattice_data(file_path)
        
        print(f"\n解析完成!")
        print(f"总共解析了 {len(lattice_data)} 个晶格结构")
        print(f"总节点数: {len(node_data)}")
        print(f"总边数: {len(edge_data)}")
        
        # 保存为CSV文件
        print("\n保存数据到CSV文件...")
        node_df, edge_df, lattice_df = save_to_csv(node_data, edge_data, lattice_data)
        
        # 显示数据统计信息
        print("\n=== 数据统计信息 ===")
        print(f"晶格结构数量: {len(lattice_df)}")
        print(f"节点数量范围: {lattice_df['num_nodes'].min()} - {lattice_df['num_nodes'].max()}")
        print(f"边数量范围: {lattice_df['num_edges'].min()} - {lattice_df['num_edges'].max()}")
        print(f"平均连接度范围: {lattice_df['avg_connectivity'].min():.2f} - {lattice_df['avg_connectivity'].max():.2f}")
        print(f"有重叠杆件的结构数量: {lattice_df['has_overlapping_bars'].sum()}")
        
        # 显示前几行数据作为示例
        print("\n=== 晶格数据示例 ===")
        print(lattice_df.head())
        
        print("\n=== 节点数据示例 ===")
        print(node_df.head())
        
        print("\n=== 边数据示例 ===")
        print(edge_df.head())
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        print("请确保 Unit_Cell.txt 文件在当前目录下")
    except Exception as e:
        print(f"解析过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()