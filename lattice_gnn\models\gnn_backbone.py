"""
GNN Backbone for Lattice Structure Processing.

This module implements the shared Graph Neural Network architecture that serves
as the backbone for both forward prediction and inverse design tasks.

The backbone uses Graph Attention Networks (GAT) with multi-head attention
and includes:
- Node and edge feature processing
- Multi-layer graph convolutions
- Global graph representation learning
- Residual connections and normalization
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATConv, GCNConv, global_mean_pool, global_max_pool, global_add_pool
from torch_geometric.nn import BatchNorm, LayerNorm
from typing import Optional, Tuple, Dict, Any
import math


class MultiHeadGATLayer(nn.Module):
    """
    Multi-head Graph Attention Network layer with residual connections.
    """
    
    def __init__(self, 
                 in_channels: int,
                 out_channels: int,
                 heads: int = 8,
                 dropout: float = 0.1,
                 edge_dim: Optional[int] = None,
                 residual: bool = True):
        """
        Initialize GAT layer.
        
        Args:
            in_channels: Input feature dimension
            out_channels: Output feature dimension
            heads: Number of attention heads
            dropout: Dropout probability
            edge_dim: Edge feature dimension
            residual: Whether to use residual connections
        """
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.heads = heads
        self.residual = residual
        
        # GAT layer
        self.gat = GATConv(
            in_channels=in_channels,
            out_channels=out_channels // heads,
            heads=heads,
            dropout=dropout,
            edge_dim=edge_dim,
            concat=True
        )
        
        # Normalization and residual
        self.norm = LayerNorm(out_channels)
        
        if residual and in_channels != out_channels:
            self.residual_proj = nn.Linear(in_channels, out_channels)
        else:
            self.residual_proj = None
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                edge_attr: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            x: Node features (num_nodes, in_channels)
            edge_index: Edge indices (2, num_edges)
            edge_attr: Edge features (num_edges, edge_dim)
            
        Returns:
            Updated node features (num_nodes, out_channels)
        """
        # Store input for residual connection
        residual = x
        
        # Apply GAT
        x = self.gat(x, edge_index, edge_attr)
        x = self.dropout(x)
        
        # Residual connection
        if self.residual:
            if self.residual_proj is not None:
                residual = self.residual_proj(residual)
            x = x + residual
        
        # Normalization
        x = self.norm(x)
        
        return x


class GraphPooling(nn.Module):
    """
    Advanced graph pooling with attention mechanism.
    """
    
    def __init__(self, 
                 hidden_dim: int,
                 pooling_type: str = 'attention',
                 num_heads: int = 4):
        """
        Initialize graph pooling.
        
        Args:
            hidden_dim: Hidden dimension
            pooling_type: Type of pooling ('attention', 'mean', 'max', 'add')
            num_heads: Number of attention heads for attention pooling
        """
        super().__init__()
        
        self.pooling_type = pooling_type
        self.hidden_dim = hidden_dim
        
        if pooling_type == 'attention':
            self.attention = nn.MultiheadAttention(
                embed_dim=hidden_dim,
                num_heads=num_heads,
                batch_first=True
            )
            # Learnable query vector for attention pooling
            self.query = nn.Parameter(torch.randn(1, 1, hidden_dim))
        
    def forward(self, x: torch.Tensor, batch: torch.Tensor) -> torch.Tensor:
        """
        Pool node features to graph-level representation.
        
        Args:
            x: Node features (num_nodes, hidden_dim)
            batch: Batch assignment (num_nodes,)
            
        Returns:
            Graph-level features (batch_size, hidden_dim)
        """
        if self.pooling_type == 'mean':
            return global_mean_pool(x, batch)
        elif self.pooling_type == 'max':
            return global_max_pool(x, batch)
        elif self.pooling_type == 'add':
            return global_add_pool(x, batch)
        elif self.pooling_type == 'attention':
            return self._attention_pooling(x, batch)
        else:
            raise ValueError(f"Unknown pooling type: {self.pooling_type}")
    
    def _attention_pooling(self, x: torch.Tensor, batch: torch.Tensor) -> torch.Tensor:
        """Attention-based pooling."""
        batch_size = batch.max().item() + 1
        pooled = []
        
        for i in range(batch_size):
            # Get nodes for this graph
            mask = batch == i
            graph_nodes = x[mask].unsqueeze(0)  # (1, num_nodes_i, hidden_dim)
            
            # Apply attention with learnable query
            query = self.query.expand(1, -1, -1)  # (1, 1, hidden_dim)
            
            attn_output, _ = self.attention(
                query=query,
                key=graph_nodes,
                value=graph_nodes
            )
            
            pooled.append(attn_output.squeeze(1))  # (1, hidden_dim)
        
        return torch.cat(pooled, dim=0)  # (batch_size, hidden_dim)


class GNNBackbone(nn.Module):
    """
    Graph Neural Network backbone for lattice structure processing.
    
    This model serves as the shared encoder for both forward prediction
    and inverse design tasks.
    """
    
    def __init__(self,
                 node_input_dim: int = 19,  # 3 coords + 16 positional encoding
                 edge_input_dim: int = 8,
                 global_input_dim: int = 10,
                 hidden_dim: int = 256,
                 num_layers: int = 4,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 pooling_type: str = 'attention',
                 use_edge_features: bool = True):
        """
        Initialize GNN backbone.
        
        Args:
            node_input_dim: Input dimension for node features
            edge_input_dim: Input dimension for edge features
            global_input_dim: Input dimension for global features
            hidden_dim: Hidden dimension throughout the network
            num_layers: Number of GNN layers
            num_heads: Number of attention heads
            dropout: Dropout probability
            pooling_type: Type of graph pooling
            use_edge_features: Whether to use edge features
        """
        super().__init__()
        
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.use_edge_features = use_edge_features
        
        # Input projections
        self.node_proj = nn.Linear(node_input_dim, hidden_dim)
        self.global_proj = nn.Linear(global_input_dim, hidden_dim)
        
        if use_edge_features:
            self.edge_proj = nn.Linear(edge_input_dim, hidden_dim)
            edge_dim = hidden_dim
        else:
            edge_dim = None
        
        # GNN layers
        self.gnn_layers = nn.ModuleList([
            MultiHeadGATLayer(
                in_channels=hidden_dim,
                out_channels=hidden_dim,
                heads=num_heads,
                dropout=dropout,
                edge_dim=edge_dim,
                residual=True
            )
            for _ in range(num_layers)
        ])
        
        # Graph pooling
        self.pooling = GraphPooling(
            hidden_dim=hidden_dim,
            pooling_type=pooling_type,
            num_heads=num_heads // 2
        )
        
        # Final projection
        self.final_proj = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # *2 for concatenation with global features
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the GNN backbone.
        
        Args:
            data: PyTorch Geometric Data object containing:
                - x: Node features (num_nodes, node_input_dim)
                - edge_index: Edge indices (2, num_edges)
                - edge_attr: Edge features (num_edges, edge_input_dim)
                - global_attr: Global features (batch_size, global_input_dim)
                - batch: Batch assignment (num_nodes,)
        
        Returns:
            Tuple of (graph_representation, node_representations)
            - graph_representation: (batch_size, hidden_dim)
            - node_representations: (num_nodes, hidden_dim)
        """
        x, edge_index, batch = data.x, data.edge_index, data.batch
        edge_attr = data.edge_attr if hasattr(data, 'edge_attr') else None
        global_attr = data.global_attr if hasattr(data, 'global_attr') else None
        
        # Project inputs
        x = self.node_proj(x)
        x = self.dropout(x)
        
        if self.use_edge_features and edge_attr is not None:
            edge_attr = self.edge_proj(edge_attr)
            edge_attr = self.dropout(edge_attr)
        
        # Apply GNN layers
        for layer in self.gnn_layers:
            x = layer(x, edge_index, edge_attr)
        
        # Store node representations
        node_representations = x
        
        # Graph-level pooling
        graph_repr = self.pooling(x, batch)
        
        # Incorporate global features
        if global_attr is not None:
            global_features = self.global_proj(global_attr)
            global_features = self.dropout(global_features)
            
            # Concatenate graph representation with global features
            graph_repr = torch.cat([graph_repr, global_features], dim=1)
        else:
            # If no global features, duplicate graph representation
            graph_repr = torch.cat([graph_repr, graph_repr], dim=1)
        
        # Final projection
        graph_repr = self.final_proj(graph_repr)
        
        return graph_repr, node_representations
    
    def get_attention_weights(self, data) -> Dict[str, torch.Tensor]:
        """
        Extract attention weights from GAT layers for visualization.
        
        Args:
            data: PyTorch Geometric Data object
            
        Returns:
            Dictionary containing attention weights for each layer
        """
        x, edge_index = data.x, data.edge_index
        edge_attr = data.edge_attr if hasattr(data, 'edge_attr') else None
        
        # Project inputs
        x = self.node_proj(x)
        
        if self.use_edge_features and edge_attr is not None:
            edge_attr = self.edge_proj(edge_attr)
        
        attention_weights = {}
        
        # Extract attention weights from each layer
        for i, layer in enumerate(self.gnn_layers):
            # This would require modifying the GAT layer to return attention weights
            # For now, we'll return empty dict
            attention_weights[f'layer_{i}'] = torch.empty(0)
        
        return attention_weights


class PositionalEncoding3D(nn.Module):
    """
    3D positional encoding for node coordinates.
    """
    
    def __init__(self, d_model: int, max_len: int = 1000):
        """
        Initialize 3D positional encoding.
        
        Args:
            d_model: Model dimension
            max_len: Maximum sequence length
        """
        super().__init__()
        
        self.d_model = d_model
        
        # Create positional encoding table
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe)
    
    def forward(self, coords: torch.Tensor) -> torch.Tensor:
        """
        Apply positional encoding to 3D coordinates.
        
        Args:
            coords: 3D coordinates (num_nodes, 3)
            
        Returns:
            Positional encodings (num_nodes, d_model)
        """
        # Scale coordinates to integer range
        scaled_coords = (coords * 100).long().clamp(0, self.pe.size(0) - 1)
        
        # Get encodings for each coordinate
        encodings = []
        for i in range(3):
            coord_encoding = self.pe[scaled_coords[:, i]]
            encodings.append(coord_encoding)
        
        # Combine encodings (simple average for now)
        combined_encoding = torch.stack(encodings).mean(dim=0)
        
        return combined_encoding


def create_gnn_backbone(config: Dict[str, Any]) -> GNNBackbone:
    """
    Factory function to create GNN backbone from configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Initialized GNN backbone
    """
    return GNNBackbone(
        node_input_dim=config.get('node_input_dim', 19),
        edge_input_dim=config.get('edge_input_dim', 8),
        global_input_dim=config.get('global_input_dim', 10),
        hidden_dim=config.get('hidden_dim', 256),
        num_layers=config.get('num_layers', 4),
        num_heads=config.get('num_heads', 8),
        dropout=config.get('dropout', 0.1),
        pooling_type=config.get('pooling_type', 'attention'),
        use_edge_features=config.get('use_edge_features', True)
    )
