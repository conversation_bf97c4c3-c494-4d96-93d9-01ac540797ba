# Configuration for Forward Prediction Model Training

# Experiment metadata
experiment:
  name: "lattice_forward_prediction"
  description: "Forward prediction of lattice properties using GNN"
  version: "1.0"
  tags: ["gnn", "forward", "lattice", "materials"]

# Data configuration
data:
  raw_dir: "data/raw"
  processed_dir: "data/processed"
  splits_dir: "data/splits"
  
  # Data splitting
  test_size: 0.15
  val_size: 0.15
  random_state: 42
  
  # Preprocessing
  normalize_features: true
  scaler_type: "standard"  # "standard" or "minmax"
  
  # Data augmentation
  augmentation:
    enabled: true
    noise_std: 0.01
    rotation_angle: 0.1

# Model architecture
model:
  # GNN Backbone
  backbone:
    node_input_dim: 19      # 3 coords + 16 positional encoding
    edge_input_dim: 8
    global_input_dim: 10
    hidden_dim: 256
    num_layers: 4
    num_heads: 8
    dropout: 0.1
    pooling_type: "attention"  # "attention", "mean", "max", "add"
    use_edge_features: true
  
  # Forward prediction head
  prediction_head:
    hidden_dims: [128, 64]
    dropout: 0.1
    activation: "relu"      # "relu", "gelu", "swish"
    use_batch_norm: true
  
  # Multi-task learning
  use_multi_task: true
  property_groups:
    mechanical_youngs: 3      # Ex, <PERSON><PERSON>, Ez
    mechanical_shear: 3       # Gy<PERSON>, Gxz, Gxy
    mechanical_poisson: 6     # Poisson ratios
    scaling_constants: 3      # Cx, Cy, Cz
    scaling_exponents: 3      # nx, ny, nz
  
  num_properties: 18

# Training configuration
training:
  # Optimization
  learning_rate: 1e-3
  weight_decay: 1e-4
  optimizer: "adamw"
  
  # Learning rate scheduling
  scheduler:
    type: "cosine"          # "cosine", "step", "plateau"
    params:
      T_max: 100
  
  # Training loop
  num_epochs: 100
  batch_size: 32
  gradient_clip: 1.0
  
  # Early stopping
  patience: 10
  min_delta: 1e-4
  
  # Loss function
  loss:
    type: "mse"             # "mse", "mae", "huber"
    property_weights: null  # Will be computed from data if null
    weight_strategy: "inverse_range"  # "inverse_range", "uniform", "normalized_inverse"
  
  # Logging and saving
  log_interval: 10
  save_best: true
  save_dir: "experiments/results"

# Evaluation configuration
evaluation:
  # Metrics to compute
  metrics:
    - "mae"
    - "rmse" 
    - "r2"
    - "mape"
    - "correlation"
  
  # Property-specific evaluation
  property_specific: true
  
  # Group-wise evaluation
  group_evaluation: true
  
  # Visualization
  plot_predictions: true
  plot_training_curves: true

# Hardware configuration
hardware:
  device: "auto"            # "auto", "cpu", "cuda"
  num_workers: 4
  pin_memory: true

# Reproducibility
seed: 42
deterministic: true

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Weights & Biases (optional)
  wandb:
    enabled: false
    project: "lattice-gnn"
    entity: null
    tags: ["forward", "gnn"]
  
  # TensorBoard (optional)
  tensorboard:
    enabled: true
    log_dir: "experiments/results/tensorboard"

# Hyperparameter search (optional)
hyperparameter_search:
  enabled: false
  method: "random"          # "random", "grid", "bayesian"
  num_trials: 50
  
  # Search space
  search_space:
    learning_rate:
      type: "loguniform"
      low: 1e-4
      high: 1e-2
    
    hidden_dim:
      type: "choice"
      choices: [128, 256, 512]
    
    num_layers:
      type: "choice"
      choices: [3, 4, 5, 6]
    
    dropout:
      type: "uniform"
      low: 0.0
      high: 0.3
