"""
Basic test script for data loading without PyTorch dependencies.

This script tests:
1. Data loading from CSV files
2. Basic data preprocessing
3. Data splitting functionality
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path


def test_basic_data_loading():
    """Test basic data loading functionality without PyTorch."""
    print("=" * 50)
    print("Testing Basic Data Loading")
    print("=" * 50)
    
    data_dir = Path("data/raw")
    
    # Check if files exist
    node_file = data_dir / "node.csv"
    edge_file = data_dir / "edge.csv"
    lattice_file = data_dir / "lattice.csv"
    
    files_exist = all(f.exists() for f in [node_file, edge_file, lattice_file])
    
    if not files_exist:
        print("✗ CSV files not found in data/raw directory")
        return None, None, None
    
    try:
        # Load data
        node_df = pd.read_csv(node_file)
        edge_df = pd.read_csv(edge_file)
        lattice_df = pd.read_csv(lattice_file)
        
        print("✓ Data loading successful")
        print(f"  - Node data shape: {node_df.shape}")
        print(f"  - Edge data shape: {edge_df.shape}")
        print(f"  - Lattice data shape: {lattice_df.shape}")
        
        # Check columns
        print(f"\nNode columns: {list(node_df.columns)}")
        print(f"Edge columns: {list(edge_df.columns)}")
        print(f"Lattice columns: {list(lattice_df.columns[:10])}...")  # First 10 columns
        
        # Check for missing values
        print(f"\nMissing values:")
        print(f"  - Nodes: {node_df.isnull().sum().sum()}")
        print(f"  - Edges: {edge_df.isnull().sum().sum()}")
        print(f"  - Lattices: {lattice_df.isnull().sum().sum()}")
        
        return node_df, edge_df, lattice_df
        
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return None, None, None


def test_data_structure_analysis(node_df, edge_df, lattice_df):
    """Analyze the structure of the loaded data."""
    print("\n" + "=" * 50)
    print("Testing Data Structure Analysis")
    print("=" * 50)
    
    if any(df is None for df in [node_df, edge_df, lattice_df]):
        print("✗ Cannot analyze data structure - data not loaded")
        return
    
    try:
        # Get unique lattice names
        lattice_names = lattice_df['lattice_name'].unique()
        print(f"✓ Found {len(lattice_names)} unique lattice structures")
        
        # Analyze first few structures
        print("\nStructure analysis for first 5 lattices:")
        for i, lattice_name in enumerate(lattice_names[:5]):
            # Get nodes for this lattice
            lattice_nodes = node_df[node_df['lattice_name'] == lattice_name]
            lattice_edges = edge_df[edge_df['lattice_name'] == lattice_name]
            
            print(f"  {i+1}. {lattice_name}:")
            print(f"     Nodes: {len(lattice_nodes)}, Edges: {len(lattice_edges)}")
            
            # Check coordinate ranges
            if len(lattice_nodes) > 0:
                x_range = (lattice_nodes['x'].min(), lattice_nodes['x'].max())
                y_range = (lattice_nodes['y'].min(), lattice_nodes['y'].max())
                z_range = (lattice_nodes['z'].min(), lattice_nodes['z'].max())
                print(f"     X range: {x_range}")
                print(f"     Y range: {y_range}")
                print(f"     Z range: {z_range}")
        
        print("✓ Data structure analysis successful")
        
    except Exception as e:
        print(f"✗ Data structure analysis failed: {e}")


def test_property_extraction(lattice_df):
    """Test extraction of property features."""
    print("\n" + "=" * 50)
    print("Testing Property Extraction")
    print("=" * 50)
    
    if lattice_df is None:
        print("✗ Cannot test property extraction - lattice data not loaded")
        return
    
    try:
        # Identify property columns
        property_cols = [col for col in lattice_df.columns 
                        if col not in ['lattice_name', 'has_overlapping_bars']]
        
        print(f"✓ Found {len(property_cols)} property columns")
        print(f"Property columns: {property_cols[:10]}...")  # First 10
        
        # Extract properties
        properties = lattice_df[property_cols].values
        print(f"✓ Property extraction successful: {properties.shape}")
        
        # Basic statistics
        print(f"\nProperty statistics:")
        print(f"  Mean: {np.mean(properties):.6f}")
        print(f"  Std: {np.std(properties):.6f}")
        print(f"  Min: {np.min(properties):.6f}")
        print(f"  Max: {np.max(properties):.6f}")
        
        # Check for mechanical properties
        mech_cols = [col for col in property_cols if col.startswith('mech_')]
        print(f"\nMechanical properties found: {len(mech_cols)}")
        for col in mech_cols[:5]:  # First 5
            values = lattice_df[col].values
            print(f"  {col}: mean={np.mean(values):.4f}, std={np.std(values):.4f}")
        
        return properties
        
    except Exception as e:
        print(f"✗ Property extraction failed: {e}")
        return None


def test_data_splitting(lattice_df):
    """Test train/validation/test splitting."""
    print("\n" + "=" * 50)
    print("Testing Data Splitting")
    print("=" * 50)
    
    if lattice_df is None:
        print("✗ Cannot test splitting - lattice data not loaded")
        return
    
    try:
        from sklearn.model_selection import train_test_split
        
        lattice_names = lattice_df['lattice_name'].tolist()
        
        # First split: train+val vs test
        train_val, test = train_test_split(
            lattice_names, 
            test_size=0.15, 
            random_state=42
        )
        
        # Second split: train vs val
        val_ratio = 0.15 / (1 - 0.15)
        train, val = train_test_split(
            train_val,
            test_size=val_ratio,
            random_state=42
        )
        
        splits = {
            'train': train,
            'val': val,
            'test': test
        }
        
        print("✓ Data splitting successful")
        print(f"  - Train: {len(train)} structures")
        print(f"  - Validation: {len(val)} structures")
        print(f"  - Test: {len(test)} structures")
        
        # Verify no overlap
        train_set = set(train)
        val_set = set(val)
        test_set = set(test)
        
        assert len(train_set & val_set) == 0, "Train and validation sets overlap"
        assert len(train_set & test_set) == 0, "Train and test sets overlap"
        assert len(val_set & test_set) == 0, "Validation and test sets overlap"
        print("✓ No overlap between splits verified")
        
        # Save splits
        splits_dir = Path("data/splits")
        splits_dir.mkdir(parents=True, exist_ok=True)
        
        for split_name, lattice_names in splits.items():
            split_file = splits_dir / f"{split_name}.txt"
            with open(split_file, 'w') as f:
                for name in lattice_names:
                    f.write(f"{name}\n")
        
        print("✓ Splits saved to data/splits/")
        
        return splits
        
    except ImportError:
        print("✗ scikit-learn not available for splitting")
        return None
    except Exception as e:
        print(f"✗ Data splitting failed: {e}")
        return None


def test_basic_statistics(node_df, edge_df, lattice_df):
    """Compute basic dataset statistics."""
    print("\n" + "=" * 50)
    print("Testing Basic Statistics")
    print("=" * 50)
    
    if any(df is None for df in [node_df, edge_df, lattice_df]):
        print("✗ Cannot compute statistics - data not loaded")
        return
    
    try:
        # Basic counts
        n_structures = len(lattice_df)
        n_total_nodes = len(node_df)
        n_total_edges = len(edge_df)
        
        print(f"Dataset Statistics:")
        print(f"  Total structures: {n_structures}")
        print(f"  Total nodes: {n_total_nodes}")
        print(f"  Total edges: {n_total_edges}")
        print(f"  Avg nodes per structure: {n_total_nodes / n_structures:.2f}")
        print(f"  Avg edges per structure: {n_total_edges / n_structures:.2f}")
        
        # Structure size distribution
        structure_sizes = []
        for lattice_name in lattice_df['lattice_name']:
            n_nodes = len(node_df[node_df['lattice_name'] == lattice_name])
            n_edges = len(edge_df[edge_df['lattice_name'] == lattice_name])
            structure_sizes.append((n_nodes, n_edges))
        
        nodes_per_struct = [size[0] for size in structure_sizes]
        edges_per_struct = [size[1] for size in structure_sizes]
        
        print(f"\nNodes per structure:")
        print(f"  Min: {min(nodes_per_struct)}")
        print(f"  Max: {max(nodes_per_struct)}")
        print(f"  Mean: {np.mean(nodes_per_struct):.2f}")
        print(f"  Std: {np.std(nodes_per_struct):.2f}")
        
        print(f"\nEdges per structure:")
        print(f"  Min: {min(edges_per_struct)}")
        print(f"  Max: {max(edges_per_struct)}")
        print(f"  Mean: {np.mean(edges_per_struct):.2f}")
        print(f"  Std: {np.std(edges_per_struct):.2f}")
        
        # Check for overlapping structures
        if 'has_overlapping_bars' in lattice_df.columns:
            overlapping = lattice_df['has_overlapping_bars'].sum()
            print(f"\nOverlapping structures: {overlapping}/{n_structures}")
        
        print("✓ Basic statistics computation successful")
        
    except Exception as e:
        print(f"✗ Basic statistics failed: {e}")


def main():
    """Run all basic tests."""
    print("Starting Basic Data Tests (No PyTorch)")
    print("=" * 70)
    
    # Test data loading
    node_df, edge_df, lattice_df = test_basic_data_loading()
    
    # Test data structure analysis
    test_data_structure_analysis(node_df, edge_df, lattice_df)
    
    # Test property extraction
    properties = test_property_extraction(lattice_df)
    
    # Test data splitting
    splits = test_data_splitting(lattice_df)
    
    # Test basic statistics
    test_basic_statistics(node_df, edge_df, lattice_df)
    
    print("\n" + "=" * 70)
    print("Basic Data Tests Completed")
    print("=" * 70)


if __name__ == "__main__":
    main()
