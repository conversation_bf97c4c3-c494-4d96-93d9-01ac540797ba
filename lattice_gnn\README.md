# Lattice GNN: PyTorch-based Graph Neural Network System

A comprehensive PyTorch-based deep learning system using Graph Neural Networks (GNN) for lattice structure performance prediction and inverse design.

## 🎯 Project Overview

This project implements a unified GNN-based system for:

1. **Forward Prediction**: Predict lattice structure performance from input structure
2. **Inverse Design**: Generate/design lattice structures from target performance requirements using diffusion models

## 📊 Dataset

- **42 unique lattice structures** from crystallographic databases (RCSR and EPINET)
- **575 nodes** total across all structures (8-36 nodes per structure)
- **790 edges** total (8-37 edges per structure)
- **28 mechanical and geometric properties** per structure including:
  - Young's moduli (Ex, Ey, Ez)
  - Shear moduli (Gyz, Gxz, Gxy)
  - <PERSON>isson's ratios (6 components)
  - Unit cell parameters (a, b, c, α, β, γ)
  - Scaling factors and exponents

## 🏗️ Architecture

### Unified GNN Backbone
- **Graph Attention Networks (GAT)** with multi-head attention
- **Multi-scale features**: Node (3D coordinates + positional encoding), edge (distances), and global features
- **Residual connections** and layer normalization
- **Attention-based graph pooling** for variable-sized structures

### Forward Prediction Branch
- **Multi-task learning** for 18 target properties
- **Property-specific heads** for different property types
- **Weighted loss functions** for balanced training

### Inverse Design Branch (Planned)
- **Diffusion-based graph generation** conditioned on target properties
- **Discrete diffusion process** for both node coordinates and edge connectivity
- **Physical validity constraints** for realistic structure generation

## 📁 Project Structure

```
lattice_gnn/
├── data/
│   ├── raw/                    # Original CSV files (node.csv, edge.csv, lattice.csv)
│   ├── processed/              # Preprocessed graph data
│   └── splits/                 # Train/val/test splits (70/15/15)
├── models/
│   ├── gnn_backbone.py         # Shared GNN architecture ✅
│   ├── forward_model.py        # Property prediction model ✅
│   ├── inverse_model.py        # Diffusion-based generation (planned)
│   └── unified_model.py        # Combined system (planned)
├── training/
│   ├── forward_trainer.py      # Forward task training ✅
│   ├── inverse_trainer.py      # Inverse task training (planned)
│   └── joint_trainer.py        # Unified training (planned)
├── utils/
│   ├── data_utils.py           # Data processing utilities ✅
│   ├── graph_utils.py          # Graph construction and manipulation ✅
│   ├── metrics.py              # Evaluation metrics ✅
│   └── visualization.py       # Result visualization (planned)
├── experiments/
│   ├── configs/                # Experiment configurations
│   ├── results/                # Training results and logs
│   └── notebooks/              # Analysis notebooks
├── tests/
│   ├── test_basic_data.py      # Basic data loading tests ✅
│   ├── test_data_pipeline.py   # Full pipeline tests ✅
│   └── test_forward_model_structure.py  # Model structure tests ✅
├── requirements.txt            # Project dependencies ✅
├── setup.py                   # Package setup ✅
└── README.md                  # This file ✅
```

## ✅ Current Progress

### Phase 1: Data Preparation and Infrastructure ✅ COMPLETE
- [x] Project directory structure created
- [x] Data loading and preprocessing pipeline implemented
- [x] Graph construction from CSV files
- [x] Data normalization and augmentation utilities
- [x] Train/validation/test splits (70/15/15)
- [x] Comprehensive data analysis and statistics

### Phase 2: Forward Prediction Model ✅ COMPLETE
- [x] GNN backbone with Graph Attention Networks
- [x] Multi-head attention and residual connections
- [x] Property prediction heads with multi-task learning
- [x] Training framework with early stopping and checkpointing
- [x] Comprehensive evaluation metrics
- [x] Model structure validation and testing

### Phase 3: Inverse Design Model 🔄 IN PROGRESS
- [ ] Diffusion model architecture for graph generation
- [ ] Conditional generation with property targets
- [ ] Structure validity constraints
- [ ] Generation quality metrics

### Phase 4: System Integration and Optimization 📋 PLANNED
- [ ] Unified training framework
- [ ] Multi-objective optimization
- [ ] Performance optimization and model compression
- [ ] Comprehensive evaluation and validation

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone the repository
cd lattice_gnn

# Install dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

### 2. Data Preparation

```python
from utils.data_utils import LatticeDataLoader

# Load and preprocess data
data_loader = LatticeDataLoader(data_dir="data/raw")
node_df, edge_df, lattice_df = data_loader.load_raw_data()

# Create train/val/test splits
splits = data_loader.create_data_splits()
data_loader.save_splits(splits)
```

### 3. Graph Construction

```python
from utils.graph_utils import LatticeGraphBuilder

# Build graphs from lattice data
graph_builder = LatticeGraphBuilder()
structure_data = data_loader.get_structure_data("cub_Z06.0_E1")
graph = graph_builder.build_graph(structure_data)
```

### 4. Model Training

```python
from models.forward_model import create_forward_model
from training.forward_trainer import create_trainer

# Create model
config = {
    'backbone': {'hidden_dim': 256, 'num_layers': 4},
    'num_properties': 18,
    'use_multi_task': True
}
model = create_forward_model(config)

# Train model
trainer = create_trainer(model, train_loader, val_loader)
history = trainer.train()
```

## 📊 Testing

Run the test suite to validate the implementation:

```bash
# Test basic data loading (no PyTorch required)
python test_basic_data.py

# Test model structure (no PyTorch required)
python test_forward_model_structure.py

# Test full pipeline (requires PyTorch)
python test_data_pipeline.py
```

## 🔧 Configuration

The system uses hierarchical configuration with sensible defaults:

```python
config = {
    'backbone': {
        'hidden_dim': 256,
        'num_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'pooling_type': 'attention'
    },
    'training': {
        'learning_rate': 1e-3,
        'num_epochs': 100,
        'batch_size': 32,
        'patience': 10
    },
    'data': {
        'test_size': 0.15,
        'val_size': 0.15,
        'normalize_features': True
    }
}
```

## 📈 Expected Performance

### Forward Prediction Targets
- **R² > 0.85** for major mechanical properties
- **Inference time < 10ms** per structure
- **Consistent performance** across different structure types

### Inverse Design Targets
- **>90% validity rate** for generated structures
- **<10% error** in achieving target properties
- **High diversity** in generated design space

## 🛠️ Dependencies

Core dependencies:
- `torch >= 2.0.0`
- `torch-geometric >= 2.3.0`
- `numpy >= 1.21.0`
- `pandas >= 1.3.0`
- `scikit-learn >= 1.0.0`

See `requirements.txt` for complete list.

## 📝 Next Steps

1. **Install PyTorch dependencies** and test model instantiation
2. **Implement inverse design model** with diffusion-based generation
3. **Create unified training framework** for joint optimization
4. **Add visualization utilities** for results analysis
5. **Optimize performance** and add model compression
6. **Comprehensive evaluation** on held-out test data

## 🤝 Contributing

This project follows a modular design with clear separation of concerns:
- **Data utilities**: Handle all data loading and preprocessing
- **Graph utilities**: Manage graph construction and manipulation
- **Models**: Implement neural network architectures
- **Training**: Provide training loops and optimization
- **Metrics**: Evaluate model performance
- **Tests**: Ensure code quality and functionality

## 📄 License

This project is developed for research purposes in materials science and machine learning.

---

**Status**: Phase 2 Complete ✅ | Phase 3 In Progress 🔄

For questions or issues, please refer to the test files and documentation in each module.
