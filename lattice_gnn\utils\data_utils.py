"""
Data utilities for Lattice GNN system.

This module provides functions for:
- Loading and preprocessing CSV data
- Data normalization and standardization
- Train/validation/test splitting
- Data augmentation strategies
"""

import pandas as pd
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import pickle
import os
from pathlib import Path


class LatticeDataLoader:
    """
    Data loader for lattice structure datasets.
    
    Handles loading, preprocessing, and splitting of lattice data from CSV files.
    """
    
    def __init__(self, data_dir: str = "data/raw"):
        """
        Initialize the data loader.
        
        Args:
            data_dir: Directory containing the raw CSV files
        """
        self.data_dir = Path(data_dir)
        self.node_data = None
        self.edge_data = None
        self.lattice_data = None
        self.scalers = {}
        
    def load_raw_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Load raw data from CSV files.
        
        Returns:
            Tuple of (node_df, edge_df, lattice_df)
        """
        node_file = self.data_dir / "node.csv"
        edge_file = self.data_dir / "edge.csv"
        lattice_file = self.data_dir / "lattice.csv"
        
        if not all(f.exists() for f in [node_file, edge_file, lattice_file]):
            raise FileNotFoundError("One or more CSV files not found in data directory")
        
        self.node_data = pd.read_csv(node_file)
        self.edge_data = pd.read_csv(edge_file)
        self.lattice_data = pd.read_csv(lattice_file)
        
        print(f"Loaded data:")
        print(f"  - Nodes: {len(self.node_data)} entries")
        print(f"  - Edges: {len(self.edge_data)} entries")
        print(f"  - Lattices: {len(self.lattice_data)} structures")
        
        return self.node_data, self.edge_data, self.lattice_data
    
    def get_lattice_names(self) -> List[str]:
        """Get list of unique lattice names."""
        if self.lattice_data is None:
            raise ValueError("Data not loaded. Call load_raw_data() first.")
        return self.lattice_data['lattice_name'].tolist()
    
    def get_structure_data(self, lattice_name: str) -> Dict:
        """
        Get all data for a specific lattice structure.
        
        Args:
            lattice_name: Name of the lattice structure
            
        Returns:
            Dictionary containing nodes, edges, and properties
        """
        if any(df is None for df in [self.node_data, self.edge_data, self.lattice_data]):
            raise ValueError("Data not loaded. Call load_raw_data() first.")
        
        # Get lattice properties
        lattice_row = self.lattice_data[self.lattice_data['lattice_name'] == lattice_name]
        if lattice_row.empty:
            raise ValueError(f"Lattice '{lattice_name}' not found")
        
        # Get nodes for this lattice
        nodes = self.node_data[self.node_data['lattice_name'] == lattice_name].copy()
        
        # Get edges for this lattice
        edges = self.edge_data[self.edge_data['lattice_name'] == lattice_name].copy()
        
        return {
            'lattice_name': lattice_name,
            'nodes': nodes,
            'edges': edges,
            'properties': lattice_row.iloc[0].to_dict()
        }
    
    def extract_property_features(self) -> np.ndarray:
        """
        Extract property features for all lattices.
        
        Returns:
            Array of shape (n_lattices, n_properties)
        """
        if self.lattice_data is None:
            raise ValueError("Data not loaded. Call load_raw_data() first.")
        
        # Define property columns (excluding metadata)
        property_cols = [col for col in self.lattice_data.columns 
                        if col not in ['lattice_name', 'has_overlapping_bars']]
        
        return self.lattice_data[property_cols].values
    
    def normalize_features(self, features: np.ndarray, 
                          scaler_type: str = 'standard',
                          fit: bool = True) -> np.ndarray:
        """
        Normalize feature arrays.
        
        Args:
            features: Feature array to normalize
            scaler_type: Type of scaler ('standard' or 'minmax')
            fit: Whether to fit the scaler (True for training data)
            
        Returns:
            Normalized features
        """
        if scaler_type not in self.scalers:
            if scaler_type == 'standard':
                self.scalers[scaler_type] = StandardScaler()
            elif scaler_type == 'minmax':
                self.scalers[scaler_type] = MinMaxScaler()
            else:
                raise ValueError(f"Unknown scaler type: {scaler_type}")
        
        scaler = self.scalers[scaler_type]
        
        if fit:
            return scaler.fit_transform(features)
        else:
            return scaler.transform(features)
    
    def create_data_splits(self, test_size: float = 0.15, 
                          val_size: float = 0.15,
                          random_state: int = 42) -> Dict[str, List[str]]:
        """
        Create train/validation/test splits.
        
        Args:
            test_size: Fraction of data for testing
            val_size: Fraction of data for validation
            random_state: Random seed for reproducibility
            
        Returns:
            Dictionary with 'train', 'val', 'test' keys containing lattice names
        """
        if self.lattice_data is None:
            raise ValueError("Data not loaded. Call load_raw_data() first.")
        
        lattice_names = self.get_lattice_names()
        
        # First split: train+val vs test
        train_val, test = train_test_split(
            lattice_names, 
            test_size=test_size, 
            random_state=random_state
        )
        
        # Second split: train vs val
        val_ratio = val_size / (1 - test_size)
        train, val = train_test_split(
            train_val,
            test_size=val_ratio,
            random_state=random_state
        )
        
        splits = {
            'train': train,
            'val': val,
            'test': test
        }
        
        print(f"Data splits created:")
        print(f"  - Train: {len(train)} structures")
        print(f"  - Validation: {len(val)} structures")
        print(f"  - Test: {len(test)} structures")
        
        return splits
    
    def save_splits(self, splits: Dict[str, List[str]], 
                   output_dir: str = "data/splits") -> None:
        """
        Save data splits to files.
        
        Args:
            splits: Dictionary containing train/val/test splits
            output_dir: Directory to save split files
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for split_name, lattice_names in splits.items():
            split_file = output_path / f"{split_name}.txt"
            with open(split_file, 'w') as f:
                for name in lattice_names:
                    f.write(f"{name}\n")
        
        print(f"Splits saved to {output_dir}")
    
    def load_splits(self, input_dir: str = "data/splits") -> Dict[str, List[str]]:
        """
        Load data splits from files.
        
        Args:
            input_dir: Directory containing split files
            
        Returns:
            Dictionary with train/val/test splits
        """
        input_path = Path(input_dir)
        splits = {}
        
        for split_name in ['train', 'val', 'test']:
            split_file = input_path / f"{split_name}.txt"
            if split_file.exists():
                with open(split_file, 'r') as f:
                    splits[split_name] = [line.strip() for line in f if line.strip()]
        
        return splits
    
    def save_scalers(self, output_dir: str = "data/processed") -> None:
        """Save fitted scalers for later use."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        scaler_file = output_path / "scalers.pkl"
        with open(scaler_file, 'wb') as f:
            pickle.dump(self.scalers, f)
        
        print(f"Scalers saved to {scaler_file}")
    
    def load_scalers(self, input_dir: str = "data/processed") -> None:
        """Load previously fitted scalers."""
        input_path = Path(input_dir)
        scaler_file = input_path / "scalers.pkl"
        
        if scaler_file.exists():
            with open(scaler_file, 'rb') as f:
                self.scalers = pickle.load(f)
            print(f"Scalers loaded from {scaler_file}")
        else:
            print(f"No scalers found at {scaler_file}")


def augment_node_coordinates(coords: np.ndarray, 
                           noise_std: float = 0.01,
                           rotation_angle: float = 0.1) -> np.ndarray:
    """
    Apply data augmentation to node coordinates.
    
    Args:
        coords: Node coordinates array of shape (n_nodes, 3)
        noise_std: Standard deviation of Gaussian noise
        rotation_angle: Maximum rotation angle in radians
        
    Returns:
        Augmented coordinates
    """
    augmented = coords.copy()
    
    # Add Gaussian noise
    noise = np.random.normal(0, noise_std, coords.shape)
    augmented += noise
    
    # Apply small random rotation
    if rotation_angle > 0:
        angle = np.random.uniform(-rotation_angle, rotation_angle)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        
        # Rotation around z-axis
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ])
        
        augmented = augmented @ rotation_matrix.T
    
    return augmented


def compute_data_statistics(data_loader: LatticeDataLoader) -> Dict:
    """
    Compute comprehensive statistics about the dataset.
    
    Args:
        data_loader: Initialized data loader
        
    Returns:
        Dictionary containing dataset statistics
    """
    if data_loader.lattice_data is None:
        raise ValueError("Data not loaded in data_loader")
    
    lattice_df = data_loader.lattice_data
    node_df = data_loader.node_data
    edge_df = data_loader.edge_data
    
    stats = {
        'n_structures': len(lattice_df),
        'n_total_nodes': len(node_df),
        'n_total_edges': len(edge_df),
        'nodes_per_structure': {
            'min': lattice_df['num_nodes'].min(),
            'max': lattice_df['num_nodes'].max(),
            'mean': lattice_df['num_nodes'].mean(),
            'std': lattice_df['num_nodes'].std()
        },
        'edges_per_structure': {
            'min': lattice_df['num_edges'].min(),
            'max': lattice_df['num_edges'].max(),
            'mean': lattice_df['num_edges'].mean(),
            'std': lattice_df['num_edges'].std()
        },
        'connectivity': {
            'min': lattice_df['avg_connectivity'].min(),
            'max': lattice_df['avg_connectivity'].max(),
            'mean': lattice_df['avg_connectivity'].mean(),
            'std': lattice_df['avg_connectivity'].std()
        },
        'overlapping_structures': lattice_df['has_overlapping_bars'].sum(),
        'property_ranges': {}
    }
    
    # Compute property ranges
    property_cols = [col for col in lattice_df.columns 
                    if col.startswith(('mech_', 'scaling_', 'unit_cell_'))]
    
    for col in property_cols:
        stats['property_ranges'][col] = {
            'min': lattice_df[col].min(),
            'max': lattice_df[col].max(),
            'mean': lattice_df[col].mean(),
            'std': lattice_df[col].std()
        }
    
    return stats
