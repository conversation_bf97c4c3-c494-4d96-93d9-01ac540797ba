# Core PyTorch and Deep Learning
torch>=2.0.0
torch-geometric>=2.3.0
torch-scatter>=2.1.0
torch-sparse>=0.6.0
torch-cluster>=1.6.0

# Scientific Computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Visualization and Plotting
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
networkx>=2.6.0

# Progress and Logging
tqdm>=4.62.0
wandb>=0.12.0
tensorboard>=2.8.0

# Configuration and Utilities
pyyaml>=6.0
hydra-core>=1.1.0
omegaconf>=2.1.0

# Testing and Development
pytest>=6.2.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0

# Optional: For advanced features
rdkit-pypi>=2022.3.0  # For molecular structure validation
ase>=3.22.0           # For atomic structure manipulation
