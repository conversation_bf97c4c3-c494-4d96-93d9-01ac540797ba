"""
Lattice GNN: PyTorch-based Graph Neural Network System for Lattice Structure 
Performance Prediction and Inverse Design

This package provides a unified framework for:
1. Forward prediction: Lattice structure → Performance properties
2. Inverse design: Target properties → Lattice structure generation

Author: AI Assistant
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

from . import models
from . import training
from . import utils

__all__ = ["models", "training", "utils"]
