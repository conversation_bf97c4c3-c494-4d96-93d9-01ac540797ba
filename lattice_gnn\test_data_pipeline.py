"""
Test script for data pipeline and graph construction.

This script tests:
1. Data loading from CSV files
2. Graph construction from lattice data
3. Data preprocessing and normalization
4. Train/validation/test splitting
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_utils import LatticeDataLoader, compute_data_statistics
from utils.graph_utils import LatticeGraphBuilder, analyze_graph_properties, compute_graph_statistics
import numpy as np
import torch


def test_data_loading():
    """Test basic data loading functionality."""
    print("=" * 50)
    print("Testing Data Loading")
    print("=" * 50)
    
    # Initialize data loader
    data_loader = LatticeDataLoader(data_dir="data/raw")
    
    # Load raw data
    try:
        node_df, edge_df, lattice_df = data_loader.load_raw_data()
        print("✓ Data loading successful")
        
        # Print basic info
        print(f"  - Node data shape: {node_df.shape}")
        print(f"  - Edge data shape: {edge_df.shape}")
        print(f"  - Lattice data shape: {lattice_df.shape}")
        
        # Check for missing values
        print(f"  - Missing values in nodes: {node_df.isnull().sum().sum()}")
        print(f"  - Missing values in edges: {edge_df.isnull().sum().sum()}")
        print(f"  - Missing values in lattices: {lattice_df.isnull().sum().sum()}")
        
        return data_loader
        
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return None


def test_graph_construction(data_loader):
    """Test graph construction from lattice data."""
    print("\n" + "=" * 50)
    print("Testing Graph Construction")
    print("=" * 50)
    
    if data_loader is None:
        print("✗ Cannot test graph construction - data loader is None")
        return []
    
    # Initialize graph builder
    graph_builder = LatticeGraphBuilder(
        node_feature_dim=16,
        edge_feature_dim=8,
        add_self_loops=True,
        normalize_coords=True
    )
    
    # Get list of lattice names
    lattice_names = data_loader.get_lattice_names()
    print(f"Found {len(lattice_names)} lattice structures")
    
    # Test graph construction for first few structures
    graphs = []
    test_structures = lattice_names[:5]  # Test first 5 structures
    
    for i, lattice_name in enumerate(test_structures):
        try:
            # Get structure data
            structure_data = data_loader.get_structure_data(lattice_name)
            
            # Build graph
            graph = graph_builder.build_graph(structure_data)
            
            # Update edge features with distances
            graph = graph_builder.update_edge_features_with_distances(graph)
            
            graphs.append(graph)
            
            print(f"✓ Graph {i+1}/{len(test_structures)} ({lattice_name}):")
            print(f"    Nodes: {graph.num_nodes}, Edges: {graph.edge_index.size(1)}")
            print(f"    Node features: {graph.x.shape}")
            print(f"    Edge features: {graph.edge_attr.shape}")
            print(f"    Target properties: {graph.y.shape}")
            print(f"    Global features: {graph.global_attr.shape}")
            
        except Exception as e:
            print(f"✗ Graph construction failed for {lattice_name}: {e}")
    
    return graphs


def test_data_preprocessing(data_loader):
    """Test data preprocessing and normalization."""
    print("\n" + "=" * 50)
    print("Testing Data Preprocessing")
    print("=" * 50)
    
    if data_loader is None:
        print("✗ Cannot test preprocessing - data loader is None")
        return
    
    try:
        # Extract property features
        properties = data_loader.extract_property_features()
        print(f"✓ Property extraction successful: {properties.shape}")
        
        # Test normalization
        normalized_props = data_loader.normalize_features(properties, 'standard', fit=True)
        print(f"✓ Standard normalization successful: {normalized_props.shape}")
        print(f"    Mean: {normalized_props.mean():.6f}")
        print(f"    Std: {normalized_props.std():.6f}")
        
        # Test MinMax normalization
        minmax_props = data_loader.normalize_features(properties, 'minmax', fit=True)
        print(f"✓ MinMax normalization successful: {minmax_props.shape}")
        print(f"    Min: {minmax_props.min():.6f}")
        print(f"    Max: {minmax_props.max():.6f}")
        
    except Exception as e:
        print(f"✗ Data preprocessing failed: {e}")


def test_data_splitting(data_loader):
    """Test train/validation/test splitting."""
    print("\n" + "=" * 50)
    print("Testing Data Splitting")
    print("=" * 50)
    
    if data_loader is None:
        print("✗ Cannot test splitting - data loader is None")
        return
    
    try:
        # Create splits
        splits = data_loader.create_data_splits(
            test_size=0.15,
            val_size=0.15,
            random_state=42
        )
        print("✓ Data splitting successful")
        
        # Verify no overlap
        train_set = set(splits['train'])
        val_set = set(splits['val'])
        test_set = set(splits['test'])
        
        assert len(train_set & val_set) == 0, "Train and validation sets overlap"
        assert len(train_set & test_set) == 0, "Train and test sets overlap"
        assert len(val_set & test_set) == 0, "Validation and test sets overlap"
        print("✓ No overlap between splits")
        
        # Save and load splits
        data_loader.save_splits(splits, "data/splits")
        loaded_splits = data_loader.load_splits("data/splits")
        
        assert splits.keys() == loaded_splits.keys(), "Split keys don't match"
        for key in splits.keys():
            assert splits[key] == loaded_splits[key], f"Split {key} doesn't match"
        print("✓ Split saving and loading successful")
        
        return splits
        
    except Exception as e:
        print(f"✗ Data splitting failed: {e}")
        return None


def test_graph_analysis(graphs):
    """Test graph analysis functions."""
    print("\n" + "=" * 50)
    print("Testing Graph Analysis")
    print("=" * 50)
    
    if not graphs:
        print("✗ Cannot test graph analysis - no graphs available")
        return
    
    try:
        # Analyze individual graphs
        print("Individual graph properties:")
        for i, graph in enumerate(graphs[:3]):  # Analyze first 3 graphs
            props = analyze_graph_properties(graph)
            print(f"  Graph {i+1}:")
            print(f"    Nodes: {props['num_nodes']}, Edges: {props['num_edges']}")
            print(f"    Density: {props['density']:.4f}")
            print(f"    Connected: {props['is_connected']}")
            print(f"    Avg degree: {props['avg_degree']:.2f}")
        
        # Compute aggregate statistics
        stats = compute_graph_statistics(graphs)
        print("\nAggregate statistics:")
        for key, values in stats.items():
            if isinstance(values, dict):
                print(f"  {key}: mean={values['mean']:.3f}, std={values['std']:.3f}")
        
        print("✓ Graph analysis successful")
        
    except Exception as e:
        print(f"✗ Graph analysis failed: {e}")


def test_dataset_statistics(data_loader):
    """Test dataset statistics computation."""
    print("\n" + "=" * 50)
    print("Testing Dataset Statistics")
    print("=" * 50)
    
    if data_loader is None:
        print("✗ Cannot test statistics - data loader is None")
        return
    
    try:
        stats = compute_data_statistics(data_loader)
        
        print("Dataset Statistics:")
        print(f"  Total structures: {stats['n_structures']}")
        print(f"  Total nodes: {stats['n_total_nodes']}")
        print(f"  Total edges: {stats['n_total_edges']}")
        
        print(f"\nNodes per structure:")
        print(f"  Min: {stats['nodes_per_structure']['min']}")
        print(f"  Max: {stats['nodes_per_structure']['max']}")
        print(f"  Mean: {stats['nodes_per_structure']['mean']:.2f}")
        
        print(f"\nEdges per structure:")
        print(f"  Min: {stats['edges_per_structure']['min']}")
        print(f"  Max: {stats['edges_per_structure']['max']}")
        print(f"  Mean: {stats['edges_per_structure']['mean']:.2f}")
        
        print(f"\nConnectivity:")
        print(f"  Min: {stats['connectivity']['min']:.2f}")
        print(f"  Max: {stats['connectivity']['max']:.2f}")
        print(f"  Mean: {stats['connectivity']['mean']:.2f}")
        
        print(f"\nOverlapping structures: {stats['overlapping_structures']}")
        
        print("✓ Dataset statistics computation successful")
        
    except Exception as e:
        print(f"✗ Dataset statistics failed: {e}")


def main():
    """Run all tests."""
    print("Starting Data Pipeline Tests")
    print("=" * 70)
    
    # Test data loading
    data_loader = test_data_loading()
    
    # Test graph construction
    graphs = test_graph_construction(data_loader)
    
    # Test data preprocessing
    test_data_preprocessing(data_loader)
    
    # Test data splitting
    splits = test_data_splitting(data_loader)
    
    # Test graph analysis
    test_graph_analysis(graphs)
    
    # Test dataset statistics
    test_dataset_statistics(data_loader)
    
    print("\n" + "=" * 70)
    print("Data Pipeline Tests Completed")
    print("=" * 70)


if __name__ == "__main__":
    main()
