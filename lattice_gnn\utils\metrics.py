"""
Evaluation metrics for Lattice GNN system.

This module provides functions for:
- Regression metrics for forward prediction
- Generation quality metrics for inverse design
- Property-specific evaluation metrics
- Visualization of results
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings


class RegressionMetrics:
    """
    Comprehensive regression metrics for property prediction.
    """
    
    @staticmethod
    def compute_all_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                           property_names: Optional[List[str]] = None) -> Dict:
        """
        Compute all regression metrics.
        
        Args:
            y_true: True values (n_samples, n_properties)
            y_pred: Predicted values (n_samples, n_properties)
            property_names: Names of properties (optional)
            
        Returns:
            Dictionary containing all metrics
        """
        if y_true.ndim == 1:
            y_true = y_true.reshape(-1, 1)
        if y_pred.ndim == 1:
            y_pred = y_pred.reshape(-1, 1)
        
        n_properties = y_true.shape[1]
        
        if property_names is None:
            property_names = [f"property_{i}" for i in range(n_properties)]
        
        metrics = {
            'overall': {},
            'per_property': {}
        }
        
        # Overall metrics (averaged across properties)
        mae_overall = np.mean([mean_absolute_error(y_true[:, i], y_pred[:, i]) 
                              for i in range(n_properties)])
        rmse_overall = np.mean([np.sqrt(mean_squared_error(y_true[:, i], y_pred[:, i])) 
                               for i in range(n_properties)])
        r2_overall = np.mean([r2_score(y_true[:, i], y_pred[:, i]) 
                             for i in range(n_properties)])
        
        metrics['overall'] = {
            'mae': mae_overall,
            'rmse': rmse_overall,
            'r2': r2_overall,
            'mape': RegressionMetrics.mean_absolute_percentage_error(y_true, y_pred)
        }
        
        # Per-property metrics
        for i, prop_name in enumerate(property_names):
            y_true_prop = y_true[:, i]
            y_pred_prop = y_pred[:, i]
            
            metrics['per_property'][prop_name] = {
                'mae': mean_absolute_error(y_true_prop, y_pred_prop),
                'rmse': np.sqrt(mean_squared_error(y_true_prop, y_pred_prop)),
                'r2': r2_score(y_true_prop, y_pred_prop),
                'mape': RegressionMetrics.mean_absolute_percentage_error(
                    y_true_prop.reshape(-1, 1), y_pred_prop.reshape(-1, 1)
                ),
                'correlation': np.corrcoef(y_true_prop, y_pred_prop)[0, 1]
            }
        
        return metrics
    
    @staticmethod
    def mean_absolute_percentage_error(y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate Mean Absolute Percentage Error (MAPE).
        
        Args:
            y_true: True values
            y_pred: Predicted values
            
        Returns:
            MAPE value
        """
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            # Avoid division by zero
            mask = np.abs(y_true) > 1e-8
            if np.sum(mask) == 0:
                return float('inf')
            
            mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
            return mape
    
    @staticmethod
    def property_group_metrics(y_true: np.ndarray, y_pred: np.ndarray,
                              property_groups: Dict[str, List[int]]) -> Dict:
        """
        Compute metrics for property groups (e.g., mechanical, geometric).
        
        Args:
            y_true: True values
            y_pred: Predicted values
            property_groups: Dictionary mapping group names to property indices
            
        Returns:
            Dictionary containing group-wise metrics
        """
        group_metrics = {}
        
        for group_name, indices in property_groups.items():
            y_true_group = y_true[:, indices]
            y_pred_group = y_pred[:, indices]
            
            group_metrics[group_name] = RegressionMetrics.compute_all_metrics(
                y_true_group, y_pred_group
            )['overall']
        
        return group_metrics


class GenerationMetrics:
    """
    Metrics for evaluating generated structures in inverse design.
    """
    
    @staticmethod
    def validity_rate(generated_structures: List[Dict]) -> float:
        """
        Calculate the rate of valid generated structures.
        
        Args:
            generated_structures: List of generated structure dictionaries
            
        Returns:
            Validity rate (0-1)
        """
        if not generated_structures:
            return 0.0
        
        valid_count = 0
        for structure in generated_structures:
            if GenerationMetrics.is_valid_structure(structure):
                valid_count += 1
        
        return valid_count / len(generated_structures)
    
    @staticmethod
    def is_valid_structure(structure: Dict) -> bool:
        """
        Check if a generated structure is valid.
        
        Args:
            structure: Structure dictionary with nodes and edges
            
        Returns:
            True if structure is valid
        """
        try:
            nodes = structure.get('nodes', [])
            edges = structure.get('edges', [])
            
            # Basic checks
            if len(nodes) == 0 or len(edges) == 0:
                return False
            
            # Check node coordinates are in valid range
            for node in nodes:
                coords = node.get('coords', [0, 0, 0])
                if not all(0 <= coord <= 1 for coord in coords):
                    return False
            
            # Check edge connectivity
            node_ids = set(node.get('id', -1) for node in nodes)
            for edge in edges:
                source = edge.get('source', -1)
                target = edge.get('target', -1)
                if source not in node_ids or target not in node_ids:
                    return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def uniqueness_rate(generated_structures: List[Dict], 
                       threshold: float = 0.1) -> float:
        """
        Calculate the rate of unique generated structures.
        
        Args:
            generated_structures: List of generated structures
            threshold: Similarity threshold for considering structures identical
            
        Returns:
            Uniqueness rate (0-1)
        """
        if len(generated_structures) <= 1:
            return 1.0
        
        unique_structures = []
        
        for structure in generated_structures:
            is_unique = True
            for unique_struct in unique_structures:
                if GenerationMetrics.structure_similarity(structure, unique_struct) > (1 - threshold):
                    is_unique = False
                    break
            
            if is_unique:
                unique_structures.append(structure)
        
        return len(unique_structures) / len(generated_structures)
    
    @staticmethod
    def structure_similarity(struct1: Dict, struct2: Dict) -> float:
        """
        Calculate similarity between two structures.
        
        Args:
            struct1: First structure
            struct2: Second structure
            
        Returns:
            Similarity score (0-1)
        """
        try:
            # Simple similarity based on number of nodes and edges
            nodes1 = len(struct1.get('nodes', []))
            nodes2 = len(struct2.get('nodes', []))
            edges1 = len(struct1.get('edges', []))
            edges2 = len(struct2.get('edges', []))
            
            if nodes1 == 0 and nodes2 == 0:
                return 1.0
            
            node_similarity = 1 - abs(nodes1 - nodes2) / max(nodes1, nodes2, 1)
            edge_similarity = 1 - abs(edges1 - edges2) / max(edges1, edges2, 1)
            
            return (node_similarity + edge_similarity) / 2
            
        except Exception:
            return 0.0
    
    @staticmethod
    def property_accuracy(generated_structures: List[Dict],
                         target_properties: List[np.ndarray],
                         tolerance: float = 0.1) -> float:
        """
        Calculate how well generated structures match target properties.
        
        Args:
            generated_structures: List of generated structures
            target_properties: List of target property arrays
            tolerance: Acceptable relative error
            
        Returns:
            Property accuracy rate (0-1)
        """
        if len(generated_structures) != len(target_properties):
            return 0.0
        
        accurate_count = 0
        
        for structure, target in zip(generated_structures, target_properties):
            predicted_props = structure.get('predicted_properties', [])
            
            if len(predicted_props) == len(target):
                relative_errors = np.abs((np.array(predicted_props) - target) / (target + 1e-8))
                if np.all(relative_errors <= tolerance):
                    accurate_count += 1
        
        return accurate_count / len(generated_structures)


class PropertySpecificMetrics:
    """
    Metrics specific to different types of properties.
    """
    
    @staticmethod
    def mechanical_property_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """
        Compute metrics specific to mechanical properties.
        
        Args:
            y_true: True mechanical properties
            y_pred: Predicted mechanical properties
            
        Returns:
            Dictionary of mechanical property metrics
        """
        # Assume mechanical properties are in specific order
        # [Ex, Ey, Ez, Gyz, Gxz, Gxy, nuyz, nuxz, nuxy, nuzy, nuzx, nuyx]
        
        if y_true.shape[1] < 12:
            return {}
        
        metrics = {}
        
        # Young's moduli (first 3)
        youngs_moduli = ['Ex', 'Ey', 'Ez']
        for i, name in enumerate(youngs_moduli):
            metrics[f'youngs_{name}'] = {
                'mae': mean_absolute_error(y_true[:, i], y_pred[:, i]),
                'r2': r2_score(y_true[:, i], y_pred[:, i])
            }
        
        # Shear moduli (next 3)
        shear_moduli = ['Gyz', 'Gxz', 'Gxy']
        for i, name in enumerate(shear_moduli, 3):
            metrics[f'shear_{name}'] = {
                'mae': mean_absolute_error(y_true[:, i], y_pred[:, i]),
                'r2': r2_score(y_true[:, i], y_pred[:, i])
            }
        
        # Poisson's ratios (next 6)
        poisson_ratios = ['nuyz', 'nuxz', 'nuxy', 'nuzy', 'nuzx', 'nuyx']
        for i, name in enumerate(poisson_ratios, 6):
            metrics[f'poisson_{name}'] = {
                'mae': mean_absolute_error(y_true[:, i], y_pred[:, i]),
                'r2': r2_score(y_true[:, i], y_pred[:, i])
            }
        
        return metrics
    
    @staticmethod
    def geometric_property_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """
        Compute metrics specific to geometric properties.
        
        Args:
            y_true: True geometric properties
            y_pred: Predicted geometric properties
            
        Returns:
            Dictionary of geometric property metrics
        """
        # Assume geometric properties include unit cell parameters
        # This would need to be adapted based on actual property ordering
        
        metrics = {}
        
        # Unit cell parameters
        if y_true.shape[1] >= 6:
            cell_params = ['a', 'b', 'c', 'alpha', 'beta', 'gamma']
            for i, name in enumerate(cell_params):
                if i < y_true.shape[1]:
                    metrics[f'cell_{name}'] = {
                        'mae': mean_absolute_error(y_true[:, i], y_pred[:, i]),
                        'r2': r2_score(y_true[:, i], y_pred[:, i])
                    }
        
        return metrics


def print_metrics_summary(metrics: Dict, title: str = "Metrics Summary"):
    """
    Print a formatted summary of metrics.
    
    Args:
        metrics: Dictionary containing metrics
        title: Title for the summary
    """
    print(f"\n{title}")
    print("=" * len(title))
    
    if 'overall' in metrics:
        print("\nOverall Metrics:")
        for metric_name, value in metrics['overall'].items():
            print(f"  {metric_name.upper()}: {value:.4f}")
    
    if 'per_property' in metrics:
        print("\nPer-Property Metrics:")
        for prop_name, prop_metrics in metrics['per_property'].items():
            print(f"\n  {prop_name}:")
            for metric_name, value in prop_metrics.items():
                print(f"    {metric_name}: {value:.4f}")


def create_property_groups() -> Dict[str, List[int]]:
    """
    Create property groups for group-wise evaluation.
    
    Returns:
        Dictionary mapping group names to property indices
    """
    # Based on the property extraction in data_utils.py
    property_groups = {
        'mechanical_youngs': [0, 1, 2],  # Ex, Ey, Ez
        'mechanical_shear': [3, 4, 5],   # Gyz, Gxz, Gxy
        'mechanical_poisson': [6, 7, 8, 9, 10, 11],  # Poisson ratios
        'scaling_constants': [12, 13, 14],  # Cx, Cy, Cz
        'scaling_exponents': [15, 16, 17]   # nx, ny, nz
    }
    
    return property_groups
