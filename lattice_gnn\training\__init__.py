"""
Training module for Lattice GNN system.

Contains:
- forward_trainer: Forward task training
- inverse_trainer: Inverse task training  
- joint_trainer: Unified training
"""

# Import will be added as trainers are implemented
# from .forward_trainer import ForwardTrainer
# from .inverse_trainer import InverseTrainer
# from .joint_trainer import JointTrainer

__all__ = []  # Will be populated as trainers are implemented
