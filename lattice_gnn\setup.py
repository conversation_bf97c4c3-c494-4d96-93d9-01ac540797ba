"""
Setup script for Lattice GNN package.
"""

from setuptools import setup, find_packages

with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="lattice-gnn",
    version="1.0.0",
    author="AI Assistant",
    description="PyTorch-based Graph Neural Network System for Lattice Structure Performance Prediction and Inverse Design",
    long_description=open("../project.md", "r", encoding="utf-8").read(),
    long_description_content_type="text/markdown",
    packages=find_packages(),
    install_requires=requirements,
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Chemistry",
        "Topic :: Scientific/Engineering :: Physics",
    ],
    keywords="graph neural networks, materials science, lattice structures, diffusion models, pytorch",
    project_urls={
        "Documentation": "https://github.com/your-repo/lattice-gnn",
        "Source": "https://github.com/your-repo/lattice-gnn",
        "Tracker": "https://github.com/your-repo/lattice-gnn/issues",
    },
)
