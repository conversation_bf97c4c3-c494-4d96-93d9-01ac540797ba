"""
Models module for Lattice GNN system.

Contains:
- gnn_backbone: Shared GNN architecture
- forward_model: Property prediction model
- inverse_model: Diffusion-based structure generation
- unified_model: Combined forward and inverse system
"""

# Import will be added as models are implemented
# from .gnn_backbone import GNNBackbone
# from .forward_model import ForwardModel
# from .inverse_model import InverseModel
# from .unified_model import UnifiedModel

__all__ = []  # Will be populated as models are implemented
