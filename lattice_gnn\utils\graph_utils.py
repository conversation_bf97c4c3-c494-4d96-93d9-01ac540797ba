"""
Graph utilities for Lattice GNN system.

This module provides functions for:
- Converting lattice data to PyTorch Geometric graphs
- Graph feature engineering
- Graph manipulation and analysis
- Batch processing utilities
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from torch_geometric.data import Data, Batch
from torch_geometric.utils import to_networkx, from_networkx
import networkx as nx
from sklearn.preprocessing import LabelEncoder
import math


class LatticeGraphBuilder:
    """
    Builder class for converting lattice data to PyTorch Geometric graphs.
    """
    
    def __init__(self, 
                 node_feature_dim: int = 16,
                 edge_feature_dim: int = 8,
                 add_self_loops: bool = True,
                 normalize_coords: bool = True):
        """
        Initialize the graph builder.
        
        Args:
            node_feature_dim: Dimension of learned node embeddings
            edge_feature_dim: Dimension of learned edge embeddings
            add_self_loops: Whether to add self-loops to graphs
            normalize_coords: Whether to normalize node coordinates
        """
        self.node_feature_dim = node_feature_dim
        self.edge_feature_dim = edge_feature_dim
        self.add_self_loops = add_self_loops
        self.normalize_coords = normalize_coords
        
    def build_graph(self, structure_data: Dict) -> Data:
        """
        Build a PyTorch Geometric graph from lattice structure data.
        
        Args:
            structure_data: Dictionary containing nodes, edges, and properties
            
        Returns:
            PyTorch Geometric Data object
        """
        nodes_df = structure_data['nodes']
        edges_df = structure_data['edges']
        properties = structure_data['properties']
        
        # Extract node features
        node_coords = nodes_df[['x', 'y', 'z']].values.astype(np.float32)
        if self.normalize_coords:
            node_coords = self._normalize_coordinates(node_coords)
        
        # Create node feature matrix
        node_features = self._create_node_features(node_coords, len(nodes_df))
        
        # Extract edge information
        edge_index, edge_features = self._create_edge_features(edges_df, len(nodes_df))
        
        # Extract target properties
        y = self._extract_target_properties(properties)
        
        # Extract global features
        global_features = self._extract_global_features(properties)
        
        # Create PyTorch Geometric Data object
        data = Data(
            x=torch.tensor(node_features, dtype=torch.float),
            edge_index=torch.tensor(edge_index, dtype=torch.long),
            edge_attr=torch.tensor(edge_features, dtype=torch.float),
            y=torch.tensor(y, dtype=torch.float),
            global_attr=torch.tensor(global_features, dtype=torch.float),
            pos=torch.tensor(node_coords, dtype=torch.float),
            lattice_name=structure_data['lattice_name'],
            num_nodes=len(nodes_df)
        )
        
        # Add self-loops if requested
        if self.add_self_loops:
            data = self._add_self_loops(data)
        
        return data
    
    def _normalize_coordinates(self, coords: np.ndarray) -> np.ndarray:
        """Normalize coordinates to unit cube."""
        coords_min = coords.min(axis=0)
        coords_max = coords.max(axis=0)
        coords_range = coords_max - coords_min
        
        # Avoid division by zero
        coords_range[coords_range == 0] = 1.0
        
        return (coords - coords_min) / coords_range
    
    def _create_node_features(self, coords: np.ndarray, num_nodes: int) -> np.ndarray:
        """
        Create node feature matrix.
        
        Args:
            coords: Node coordinates (n_nodes, 3)
            num_nodes: Number of nodes
            
        Returns:
            Node feature matrix (n_nodes, feature_dim)
        """
        features = []
        
        # Add 3D coordinates
        features.append(coords)
        
        # Add positional encodings
        pos_encoding = self._positional_encoding_3d(coords, self.node_feature_dim - 3)
        features.append(pos_encoding)
        
        return np.concatenate(features, axis=1)
    
    def _positional_encoding_3d(self, coords: np.ndarray, encoding_dim: int) -> np.ndarray:
        """
        Create 3D positional encodings for node coordinates.
        
        Args:
            coords: Node coordinates (n_nodes, 3)
            encoding_dim: Dimension of positional encoding
            
        Returns:
            Positional encodings (n_nodes, encoding_dim)
        """
        n_nodes = coords.shape[0]
        encoding = np.zeros((n_nodes, encoding_dim))
        
        # Use sinusoidal encodings for each coordinate
        for i in range(3):  # x, y, z coordinates
            coord = coords[:, i]
            
            # Number of encoding dimensions per coordinate
            dim_per_coord = encoding_dim // 3
            start_idx = i * dim_per_coord
            end_idx = start_idx + dim_per_coord
            
            for j in range(dim_per_coord // 2):
                div_term = np.exp(j * -(np.log(10000.0) / (dim_per_coord // 2)))
                encoding[:, start_idx + 2*j] = np.sin(coord * div_term)
                encoding[:, start_idx + 2*j + 1] = np.cos(coord * div_term)
        
        return encoding
    
    def _create_edge_features(self, edges_df: pd.DataFrame, 
                            num_nodes: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create edge index and edge features.
        
        Args:
            edges_df: DataFrame containing edge information
            num_nodes: Number of nodes in the graph
            
        Returns:
            Tuple of (edge_index, edge_features)
        """
        # Convert to 0-based indexing
        edge_index = edges_df[['source_node_original', 'target_node_original']].values - 1
        edge_index = edge_index.T  # Shape: (2, num_edges)
        
        # Create edge features
        edge_features = []
        
        # Add edge distances (will be computed from node positions)
        # For now, add placeholder features
        num_edges = edge_index.shape[1]
        edge_features = np.ones((num_edges, self.edge_feature_dim))
        
        return edge_index, edge_features
    
    def _extract_target_properties(self, properties: Dict) -> np.ndarray:
        """
        Extract target properties for prediction.
        
        Args:
            properties: Dictionary containing lattice properties
            
        Returns:
            Array of target properties
        """
        # Define property keys to extract
        property_keys = [
            'mech_Ex', 'mech_Ey', 'mech_Ez',
            'mech_Gyz', 'mech_Gxz', 'mech_Gxy',
            'mech_nuyz', 'mech_nuxz', 'mech_nuxy',
            'mech_nuzy', 'mech_nuzx', 'mech_nuyx',
            'scaling_Cx', 'scaling_Cy', 'scaling_Cz',
            'scaling_nx', 'scaling_ny', 'scaling_nz'
        ]
        
        targets = []
        for key in property_keys:
            if key in properties:
                targets.append(properties[key])
            else:
                targets.append(0.0)  # Default value for missing properties
        
        return np.array(targets, dtype=np.float32)
    
    def _extract_global_features(self, properties: Dict) -> np.ndarray:
        """
        Extract global graph features.
        
        Args:
            properties: Dictionary containing lattice properties
            
        Returns:
            Array of global features
        """
        global_features = [
            properties.get('unit_cell_a', 1.0),
            properties.get('unit_cell_b', 1.0),
            properties.get('unit_cell_c', 1.0),
            properties.get('unit_cell_alpha', 90.0),
            properties.get('unit_cell_beta', 90.0),
            properties.get('unit_cell_gamma', 90.0),
            properties.get('avg_connectivity', 6.0),
            properties.get('num_nodes', 8),
            properties.get('num_edges', 12),
            float(properties.get('has_overlapping_bars', False))
        ]
        
        return np.array(global_features, dtype=np.float32)
    
    def _add_self_loops(self, data: Data) -> Data:
        """Add self-loops to the graph."""
        num_nodes = data.num_nodes
        
        # Create self-loop edges
        self_loop_index = torch.arange(num_nodes).unsqueeze(0).repeat(2, 1)
        
        # Concatenate with existing edges
        data.edge_index = torch.cat([data.edge_index, self_loop_index], dim=1)
        
        # Add self-loop edge features (zeros)
        self_loop_attr = torch.zeros(num_nodes, data.edge_attr.size(1))
        data.edge_attr = torch.cat([data.edge_attr, self_loop_attr], dim=0)
        
        return data
    
    def compute_edge_distances(self, data: Data) -> torch.Tensor:
        """
        Compute Euclidean distances for edges.
        
        Args:
            data: PyTorch Geometric Data object
            
        Returns:
            Edge distances tensor
        """
        pos = data.pos
        edge_index = data.edge_index
        
        # Get source and target node positions
        source_pos = pos[edge_index[0]]
        target_pos = pos[edge_index[1]]
        
        # Compute Euclidean distances
        distances = torch.norm(source_pos - target_pos, dim=1, keepdim=True)
        
        return distances
    
    def update_edge_features_with_distances(self, data: Data) -> Data:
        """Update edge features with computed distances."""
        distances = self.compute_edge_distances(data)
        
        # Replace first feature with distance
        data.edge_attr[:, 0:1] = distances
        
        return data


class GraphBatchProcessor:
    """
    Utility class for batch processing of graphs.
    """
    
    @staticmethod
    def create_batch(graph_list: List[Data]) -> Batch:
        """
        Create a batch from a list of graphs.
        
        Args:
            graph_list: List of PyTorch Geometric Data objects
            
        Returns:
            Batched graphs
        """
        return Batch.from_data_list(graph_list)
    
    @staticmethod
    def pad_graphs_to_max_size(graph_list: List[Data], 
                              max_nodes: Optional[int] = None) -> List[Data]:
        """
        Pad graphs to the same size for easier batching.
        
        Args:
            graph_list: List of graphs to pad
            max_nodes: Maximum number of nodes (if None, use max in batch)
            
        Returns:
            List of padded graphs
        """
        if max_nodes is None:
            max_nodes = max(data.num_nodes for data in graph_list)
        
        padded_graphs = []
        for data in graph_list:
            if data.num_nodes < max_nodes:
                # Pad node features
                padding_size = max_nodes - data.num_nodes
                node_padding = torch.zeros(padding_size, data.x.size(1))
                data.x = torch.cat([data.x, node_padding], dim=0)
                
                # Update position matrix
                pos_padding = torch.zeros(padding_size, 3)
                data.pos = torch.cat([data.pos, pos_padding], dim=0)
                
                # Note: Edge information remains unchanged
                # This creates a sparse adjacency representation
            
            padded_graphs.append(data)
        
        return padded_graphs


def analyze_graph_properties(data: Data) -> Dict:
    """
    Analyze properties of a single graph.
    
    Args:
        data: PyTorch Geometric Data object
        
    Returns:
        Dictionary containing graph properties
    """
    # Convert to NetworkX for analysis
    G = to_networkx(data, to_undirected=True)
    
    properties = {
        'num_nodes': data.num_nodes,
        'num_edges': data.edge_index.size(1),
        'density': nx.density(G),
        'is_connected': nx.is_connected(G),
        'num_components': nx.number_connected_components(G),
        'average_clustering': nx.average_clustering(G),
        'diameter': nx.diameter(G) if nx.is_connected(G) else float('inf'),
        'average_path_length': nx.average_shortest_path_length(G) if nx.is_connected(G) else float('inf')
    }
    
    # Degree statistics
    degrees = [G.degree(n) for n in G.nodes()]
    properties.update({
        'min_degree': min(degrees),
        'max_degree': max(degrees),
        'avg_degree': np.mean(degrees),
        'degree_std': np.std(degrees)
    })
    
    return properties


def compute_graph_statistics(graph_list: List[Data]) -> Dict:
    """
    Compute statistics across a list of graphs.
    
    Args:
        graph_list: List of PyTorch Geometric Data objects
        
    Returns:
        Dictionary containing aggregate statistics
    """
    all_properties = [analyze_graph_properties(data) for data in graph_list]
    
    # Aggregate statistics
    stats = {}
    for key in all_properties[0].keys():
        values = [props[key] for props in all_properties if not math.isinf(props[key])]
        if values:
            stats[key] = {
                'min': min(values),
                'max': max(values),
                'mean': np.mean(values),
                'std': np.std(values)
            }
    
    return stats
